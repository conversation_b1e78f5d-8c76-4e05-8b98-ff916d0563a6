<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class AnalyticsController extends Controller
{
    /**
     * Display the analytics dashboard page
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get user's accommodations and groups for filtering
        $accommodations = $user->accommodations()
            ->select('id', 'name', 'accommodation_group_id')
            ->orderBy('name')
            ->get();
            
        $accommodationGroups = $user->accommodationGroups()
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return Inertia::render('Analytics', [
            'accommodations' => $accommodations,
            'accommodationGroups' => $accommodationGroups
        ]);
    }
}
