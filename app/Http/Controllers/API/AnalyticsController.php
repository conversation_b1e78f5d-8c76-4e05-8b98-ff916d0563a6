<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\AnalyticsService;
use App\Services\AnalyticsInsightsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AnalyticsController extends Controller
{
    protected $analyticsService;
    protected $insightsService;

    public function __construct(AnalyticsService $analyticsService, AnalyticsInsightsService $insightsService)
    {
        $this->analyticsService = $analyticsService;
        $this->insightsService = $insightsService;
    }

    /**
     * Get comprehensive dashboard analytics data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboard(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id',
            'period' => 'nullable|in:7,30,90,custom'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $period = $request->input('period', '30');
        
        // Set date range based on period
        if ($period === 'custom') {
            $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subDays(30);
            $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();
        } else {
            $days = (int) $period;
            $startDate = Carbon::now()->subDays($days);
            $endDate = Carbon::now();
        }

        // Filter accommodations to only user's accommodations
        $accommodationIds = $request->input('accommodation_ids', []);
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = $this->analyticsService->getDashboardSummary($user, $startDate, $endDate, $accommodationIds);

            // Generate insights
            $insights = $this->insightsService->generateAllInsights($data);

            return response()->json([
                'success' => true,
                'data' => $data,
                'insights' => $insights,
                'meta' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'period' => $period,
                    'accommodation_ids' => $accommodationIds
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch analytics data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get accommodation popularity ranking
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPopularity(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $accommodationIds = $request->input('accommodation_ids', []);

        // Filter to user's accommodations only
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = $this->analyticsService->getAccommodationPopularity($user, $startDate, $endDate, $accommodationIds);
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch popularity data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get date range and seasonal analysis
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDateAnalysis(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $accommodationIds = $request->input('accommodation_ids', []);

        // Filter to user's accommodations only
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = $this->analyticsService->getDateRangeAnalysis($user, $startDate, $endDate, $accommodationIds);
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch date analysis data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unavailability reasons analysis
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnavailabilityReasons(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $accommodationIds = $request->input('accommodation_ids', []);

        // Filter to user's accommodations only
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = $this->analyticsService->getUnavailabilityReasons($user, $startDate, $endDate, $accommodationIds);
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch unavailability reasons data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get conversion ratio metrics
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConversionMetrics(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $accommodationIds = $request->input('accommodation_ids', []);

        // Filter to user's accommodations only
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = $this->analyticsService->getConversionRatio($user, $startDate, $endDate, $accommodationIds);
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch conversion metrics data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get search trends over time
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSearchTrends(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id',
            'interval' => 'nullable|in:daily,weekly,monthly'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $accommodationIds = $request->input('accommodation_ids', []);
        $interval = $request->input('interval', 'daily');

        // Filter to user's accommodations only
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = $this->analyticsService->getSearchTrends($user, $startDate, $endDate, $accommodationIds, $interval);
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch search trends data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export analytics data as CSV
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function exportCsv(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'accommodation_ids' => 'nullable|array',
            'accommodation_ids.*' => 'integer|exists:accommodations,id',
            'type' => 'required|in:popularity,date_analysis,unavailability,conversion,trends'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $accommodationIds = $request->input('accommodation_ids', []);
        $type = $request->input('type');

        // Filter to user's accommodations only
        if (!empty($accommodationIds)) {
            $userAccommodationIds = $user->accommodations()->pluck('id')->toArray();
            $accommodationIds = array_intersect($accommodationIds, $userAccommodationIds);
        }

        try {
            $data = match($type) {
                'popularity' => $this->analyticsService->getAccommodationPopularity($user, $startDate, $endDate, $accommodationIds),
                'date_analysis' => $this->analyticsService->getDateRangeAnalysis($user, $startDate, $endDate, $accommodationIds),
                'unavailability' => $this->analyticsService->getUnavailabilityReasons($user, $startDate, $endDate, $accommodationIds),
                'conversion' => $this->analyticsService->getConversionRatio($user, $startDate, $endDate, $accommodationIds),
                'trends' => $this->analyticsService->getSearchTrends($user, $startDate, $endDate, $accommodationIds, 'daily'),
                default => []
            };

            $filename = "analytics_{$type}_{$startDate->format('Y-m-d')}_to_{$endDate->format('Y-m-d')}.csv";
            
            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function() use ($data, $type) {
                $file = fopen('php://output', 'w');
                
                // Write CSV headers based on data type
                $this->writeCsvHeaders($file, $type);
                
                // Write data rows
                $this->writeCsvData($file, $data, $type);
                
                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to export data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Write CSV headers based on data type
     */
    private function writeCsvHeaders($file, $type)
    {
        $headers = match($type) {
            'popularity' => ['Accommodation Name', 'Search Count', 'Available Searches', 'Unavailable Searches', 'Availability Rate %', 'Avg Quoted Price'],
            'unavailability' => ['Reason', 'Count', 'Percentage'],
            'conversion' => ['Metric', 'Value'],
            'trends' => ['Period', 'Search Count', 'Available Count', 'Availability Rate %', 'Avg Price'],
            default => ['Data']
        };
        
        fputcsv($file, $headers);
    }

    /**
     * Write CSV data based on data type
     */
    private function writeCsvData($file, $data, $type)
    {
        switch($type) {
            case 'popularity':
                foreach ($data as $row) {
                    fputcsv($file, [
                        $row['accommodation_name'],
                        $row['search_count'],
                        $row['available_searches'],
                        $row['unavailable_searches'],
                        $row['availability_rate'],
                        $row['avg_quoted_price']
                    ]);
                }
                break;
            case 'unavailability':
                foreach ($data as $row) {
                    fputcsv($file, [$row['reason'], $row['count'], $row['percentage']]);
                }
                break;
            case 'conversion':
                fputcsv($file, ['Total Searches', $data['total_searches']]);
                fputcsv($file, ['Available Searches', $data['available_searches']]);
                fputcsv($file, ['Confirmed Bookings', $data['confirmed_bookings']]);
                fputcsv($file, ['Conversion Rate %', $data['conversion_rate']]);
                fputcsv($file, ['Availability Rate %', $data['availability_rate']]);
                break;
            case 'trends':
                foreach ($data as $row) {
                    fputcsv($file, [
                        $row['period'],
                        $row['search_count'],
                        $row['available_count'],
                        $row['availability_rate'],
                        $row['avg_price']
                    ]);
                }
                break;
        }
    }
}
