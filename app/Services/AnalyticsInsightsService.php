<?php

namespace App\Services;

use Carbon\Carbon;

class AnalyticsInsightsService
{
    /**
     * Generate insights for conversion metrics
     *
     * @param array $conversionData
     * @return array
     */
    public function generateConversionInsights(array $conversionData): array
    {
        $insights = [];
        
        $conversionRate = $conversionData['conversion_rate'] ?? 0;
        $availabilityRate = $conversionData['availability_rate'] ?? 0;
        $totalSearches = $conversionData['total_searches'] ?? 0;
        
        // Conversion rate insights
        if ($conversionRate < 5) {
            $insights[] = [
                'type' => 'warning',
                'metric' => 'conversion_rate',
                'title' => 'Low Conversion Rate',
                'message' => 'Your conversion rate is below 5%. Consider improving your pricing strategy or property descriptions.',
                'action' => 'Review pricing and property photos'
            ];
        } elseif ($conversionRate > 15) {
            $insights[] = [
                'type' => 'success',
                'metric' => 'conversion_rate',
                'title' => 'Excellent Conversion Rate',
                'message' => 'Your conversion rate is excellent! Consider increasing prices slightly to maximize revenue.',
                'action' => 'Test higher pricing'
            ];
        }
        
        // Availability rate insights
        if ($availabilityRate < 60) {
            $insights[] = [
                'type' => 'warning',
                'metric' => 'availability_rate',
                'title' => 'Low Availability',
                'message' => 'You\'re missing out on bookings due to low availability. Consider adjusting your calendar.',
                'action' => 'Review blocked dates and pricing'
            ];
        }
        
        // Search volume insights
        if ($totalSearches < 10) {
            $insights[] = [
                'type' => 'info',
                'metric' => 'search_volume',
                'title' => 'Low Search Volume',
                'message' => 'Consider improving your property visibility through better SEO and marketing.',
                'action' => 'Optimize property listings'
            ];
        }
        
        return $insights;
    }

    /**
     * Generate insights for popularity data
     *
     * @param array $popularityData
     * @return array
     */
    public function generatePopularityInsights(array $popularityData): array
    {
        $insights = [];
        
        if (empty($popularityData)) {
            return $insights;
        }
        
        $topProperty = $popularityData[0] ?? null;
        $totalProperties = count($popularityData);
        
        if ($topProperty && $totalProperties > 1) {
            $topSearches = $topProperty['search_count'];
            $avgSearches = array_sum(array_column($popularityData, 'search_count')) / $totalProperties;
            
            if ($topSearches > $avgSearches * 2) {
                $insights[] = [
                    'type' => 'success',
                    'metric' => 'popularity',
                    'title' => 'Star Property Identified',
                    'message' => "'{$topProperty['accommodation_name']}' is your most popular property. Consider replicating its success factors.",
                    'action' => 'Analyze what makes this property successful'
                ];
            }
        }
        
        // Check for properties with low search volume
        $lowPerformers = array_filter($popularityData, function($property) {
            return $property['search_count'] < 5;
        });
        
        if (count($lowPerformers) > 0) {
            $insights[] = [
                'type' => 'warning',
                'metric' => 'popularity',
                'title' => 'Underperforming Properties',
                'message' => count($lowPerformers) . ' properties have very low search volume. Consider improving their listings.',
                'action' => 'Update photos, descriptions, and pricing'
            ];
        }
        
        return $insights;
    }

    /**
     * Generate insights for unavailability reasons
     *
     * @param array $unavailabilityData
     * @return array
     */
    public function generateUnavailabilityInsights(array $unavailabilityData): array
    {
        $insights = [];
        
        if (empty($unavailabilityData)) {
            return $insights;
        }
        
        $topReason = $unavailabilityData[0] ?? null;
        
        if ($topReason) {
            $reason = $topReason['reason'];
            $percentage = $topReason['percentage'];
            
            switch (strtolower($reason)) {
                case 'already booked':
                    if ($percentage > 50) {
                        $insights[] = [
                            'type' => 'success',
                            'metric' => 'unavailability',
                            'title' => 'High Booking Rate',
                            'message' => 'Most unavailability is due to existing bookings - great occupancy!',
                            'action' => 'Consider raising prices during peak periods'
                        ];
                    }
                    break;
                    
                case 'minimum stay not met':
                    if ($percentage > 30) {
                        $insights[] = [
                            'type' => 'warning',
                            'metric' => 'unavailability',
                            'title' => 'Minimum Stay Issues',
                            'message' => 'Many searches fail due to minimum stay requirements. Consider flexibility.',
                            'action' => 'Review and adjust minimum stay policies'
                        ];
                    }
                    break;
                    
                case 'blocked by owner':
                    if ($percentage > 25) {
                        $insights[] = [
                            'type' => 'info',
                            'metric' => 'unavailability',
                            'title' => 'Owner Blocks',
                            'message' => 'You\'re blocking many potential bookings. Review if all blocks are necessary.',
                            'action' => 'Optimize calendar availability'
                        ];
                    }
                    break;
            }
        }
        
        return $insights;
    }

    /**
     * Generate insights for date range analysis
     *
     * @param array $dateAnalysis
     * @return array
     */
    public function generateDateInsights(array $dateAnalysis): array
    {
        $insights = [];
        
        // Analyze monthly patterns
        if (!empty($dateAnalysis['monthly'])) {
            $monthlyData = $dateAnalysis['monthly'];
            $maxSearches = max(array_column($monthlyData, 'search_count'));
            $minSearches = min(array_column($monthlyData, 'search_count'));
            
            if ($maxSearches > $minSearches * 3) {
                $peakMonth = collect($monthlyData)->firstWhere('search_count', $maxSearches);
                $insights[] = [
                    'type' => 'info',
                    'metric' => 'seasonality',
                    'title' => 'Strong Seasonal Pattern',
                    'message' => "Peak demand in {$peakMonth['period']}. Optimize pricing for seasonal variations.",
                    'action' => 'Implement seasonal pricing strategy'
                ];
            }
        }
        
        // Analyze day of week patterns
        if (!empty($dateAnalysis['day_of_week'])) {
            $weekendDays = ['Friday', 'Saturday', 'Sunday'];
            $weekdayDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday'];
            
            $weekendSearches = 0;
            $weekdaySearches = 0;
            
            foreach ($dateAnalysis['day_of_week'] as $dayData) {
                if (in_array($dayData['day'], $weekendDays)) {
                    $weekendSearches += $dayData['search_count'];
                } else {
                    $weekdaySearches += $dayData['search_count'];
                }
            }
            
            if ($weekendSearches > $weekdaySearches * 1.5) {
                $insights[] = [
                    'type' => 'info',
                    'metric' => 'day_patterns',
                    'title' => 'Weekend Preference',
                    'message' => 'Strong weekend demand detected. Consider weekend premium pricing.',
                    'action' => 'Implement weekend pricing strategy'
                ];
            }
        }
        
        return $insights;
    }

    /**
     * Generate insights for search trends
     *
     * @param array $trendsData
     * @return array
     */
    public function generateTrendsInsights(array $trendsData): array
    {
        $insights = [];
        
        if (count($trendsData) < 7) {
            return $insights; // Need at least a week of data
        }
        
        // Calculate trend direction
        $recentData = array_slice($trendsData, -7); // Last 7 days
        $olderData = array_slice($trendsData, -14, 7); // Previous 7 days
        
        $recentAvg = array_sum(array_column($recentData, 'search_count')) / count($recentData);
        $olderAvg = array_sum(array_column($olderData, 'search_count')) / count($olderData);
        
        $trendChange = (($recentAvg - $olderAvg) / $olderAvg) * 100;
        
        if ($trendChange > 20) {
            $insights[] = [
                'type' => 'success',
                'metric' => 'trends',
                'title' => 'Growing Demand',
                'message' => 'Search volume is trending upward. Great time to optimize pricing.',
                'action' => 'Consider increasing rates for upcoming dates'
            ];
        } elseif ($trendChange < -20) {
            $insights[] = [
                'type' => 'warning',
                'metric' => 'trends',
                'title' => 'Declining Interest',
                'message' => 'Search volume is declining. Consider promotional strategies.',
                'action' => 'Review pricing and marketing efforts'
            ];
        }
        
        return $insights;
    }

    /**
     * Generate comprehensive insights for all analytics data
     *
     * @param array $analyticsData
     * @return array
     */
    public function generateAllInsights(array $analyticsData): array
    {
        $allInsights = [];
        
        if (isset($analyticsData['conversion_metrics'])) {
            $allInsights = array_merge($allInsights, $this->generateConversionInsights($analyticsData['conversion_metrics']));
        }
        
        if (isset($analyticsData['popularity'])) {
            $allInsights = array_merge($allInsights, $this->generatePopularityInsights($analyticsData['popularity']));
        }
        
        if (isset($analyticsData['unavailability_reasons'])) {
            $allInsights = array_merge($allInsights, $this->generateUnavailabilityInsights($analyticsData['unavailability_reasons']));
        }
        
        if (isset($analyticsData['date_analysis'])) {
            $allInsights = array_merge($allInsights, $this->generateDateInsights($analyticsData['date_analysis']));
        }
        
        if (isset($analyticsData['search_trends'])) {
            $allInsights = array_merge($allInsights, $this->generateTrendsInsights($analyticsData['search_trends']));
        }
        
        // Sort insights by priority (warnings first, then info, then success)
        usort($allInsights, function($a, $b) {
            $priority = ['warning' => 1, 'info' => 2, 'success' => 3];
            return $priority[$a['type']] <=> $priority[$b['type']];
        });
        
        return $allInsights;
    }

    /**
     * Get tooltip explanations for metrics
     *
     * @param string $metric
     * @return string|null
     */
    public function getMetricTooltip(string $metric): ?string
    {
        $tooltips = [
            'conversion_rate' => 'Percentage of available searches that resulted in confirmed bookings. Higher is better.',
            'availability_rate' => 'Percentage of searches where your property was available. Low rates indicate high occupancy or calendar blocks.',
            'search_volume' => 'Total number of searches for your properties. Indicates market interest and visibility.',
            'confirmed_bookings' => 'Number of bookings that were confirmed during this period.',
            'popularity_ranking' => 'Properties ranked by search volume. Higher search volume indicates greater market interest.',
            'unavailability_reasons' => 'Why searches couldn\'t be converted to bookings. Helps identify optimization opportunities.',
            'seasonal_patterns' => 'Search patterns by month and day of week. Useful for pricing and availability strategies.',
            'search_trends' => 'Search volume over time. Shows demand trends and helps predict future booking patterns.'
        ];
        
        return $tooltips[$metric] ?? null;
    }
}
