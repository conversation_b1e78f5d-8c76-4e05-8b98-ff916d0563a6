<?php

namespace App\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AnalyticsCacheService
{
    /**
     * Cache TTL in seconds
     */
    const CACHE_TTL = [
        'dashboard' => 3600,      // 1 hour for dashboard summary
        'popularity' => 3600,     // 1 hour for popularity data
        'date_analysis' => 7200,  // 2 hours for date analysis
        'unavailability' => 1800, // 30 minutes for unavailability reasons
        'conversion' => 1800,     // 30 minutes for conversion metrics
        'trends' => 900,          // 15 minutes for search trends
    ];

    /**
     * Generate cache key for analytics data
     *
     * @param string $type
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds
     * @param array $additionalParams
     * @return string
     */
    public function generateCacheKey(
        string $type,
        User $user,
        Carbon $startDate,
        Carbon $endDate,
        array $accommodationIds = [],
        array $additionalParams = []
    ): string {
        $baseKey = "analytics_{$type}_{$user->id}_{$startDate->format('Y-m-d')}_{$endDate->format('Y-m-d')}";

        // Ensure accommodationIds is always an array and handle sorting safely
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];
        if (!empty($accommodationIds)) {
            $sortedIds = $accommodationIds;
            sort($sortedIds);
            $baseKey .= '_' . md5(implode(',', $sortedIds));
        }

        if (!empty($additionalParams)) {
            $baseKey .= '_' . md5(serialize($additionalParams));
        }

        return $baseKey;
    }

    /**
     * Get cached data or execute callback and cache result
     *
     * @param string $cacheKey
     * @param string $type
     * @param callable $callback
     * @return mixed
     */
    public function remember(string $cacheKey, string $type, callable $callback)
    {
        $ttl = self::CACHE_TTL[$type] ?? 3600;
        
        try {
            return Cache::remember($cacheKey, $ttl, $callback);
        } catch (\Exception $e) {
            Log::warning("Analytics cache failed for key: {$cacheKey}", [
                'error' => $e->getMessage(),
                'type' => $type
            ]);
            
            // Fallback to direct execution if cache fails
            return $callback();
        }
    }

    /**
     * Clear all analytics cache for a user
     *
     * @param User $user
     * @return void
     */
    public function clearUserCache(User $user): void
    {
        $patterns = [
            "analytics_dashboard_{$user->id}_*",
            "analytics_popularity_{$user->id}_*",
            "analytics_date_analysis_{$user->id}_*",
            "analytics_unavailability_{$user->id}_*",
            "analytics_conversion_{$user->id}_*",
            "analytics_trends_{$user->id}_*"
        ];

        foreach ($patterns as $pattern) {
            $this->clearCacheByPattern($pattern);
        }
    }

    /**
     * Clear cache for specific accommodation
     *
     * @param int $accommodationId
     * @return void
     */
    public function clearAccommodationCache(int $accommodationId): void
    {
        // This would require a more sophisticated cache tagging system
        // For now, we'll clear all analytics cache when accommodation data changes
        $this->clearAllAnalyticsCache();
    }

    /**
     * Clear all analytics cache (use sparingly)
     *
     * @return void
     */
    public function clearAllAnalyticsCache(): void
    {
        $patterns = [
            'analytics_dashboard_*',
            'analytics_popularity_*',
            'analytics_date_analysis_*',
            'analytics_unavailability_*',
            'analytics_conversion_*',
            'analytics_trends_*'
        ];

        foreach ($patterns as $pattern) {
            $this->clearCacheByPattern($pattern);
        }
    }

    /**
     * Clear cache by pattern (Redis specific, with fallback for other drivers)
     *
     * @param string $pattern
     * @return void
     */
    private function clearCacheByPattern(string $pattern): void
    {
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys($pattern);

                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // For non-Redis cache drivers, we'll use a fallback approach
                // This is less efficient but works with all cache drivers
                $this->clearCacheByPatternFallback($pattern);
            }
        } catch (\Exception $e) {
            Log::error("Failed to clear cache pattern: {$pattern}", [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Fallback method to clear cache patterns for non-Redis drivers
     * This generates common cache keys and attempts to clear them
     *
     * @param string $pattern
     * @return void
     */
    private function clearCacheByPatternFallback(string $pattern): void
    {
        // Extract the base pattern and user ID if present
        if (preg_match('/analytics_(\w+)_(\d+)_\*/', $pattern, $matches)) {
            $type = $matches[1];
            $userId = $matches[2];

            // Generate common date ranges to clear
            $dateRanges = [
                // Common preset periods
                [Carbon::now()->subDays(7), Carbon::now()],
                [Carbon::now()->subDays(30), Carbon::now()],
                [Carbon::now()->subDays(90), Carbon::now()],
                // Recent months
                [Carbon::now()->startOfMonth(), Carbon::now()],
                [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()],
                [Carbon::now()->subMonths(2)->startOfMonth(), Carbon::now()->subMonths(2)->endOfMonth()],
            ];

            foreach ($dateRanges as [$startDate, $endDate]) {
                // Generate cache keys manually to avoid User model dependency
                $baseKey = "analytics_{$type}_{$userId}";
                $dateKey = $startDate->format('Y-m-d') . '_' . $endDate->format('Y-m-d');

                // Try common cache key patterns
                $cacheKeys = [
                    "{$baseKey}_{$dateKey}",
                    "{$baseKey}_{$dateKey}_",
                    "{$baseKey}_{$dateKey}_all",
                    "{$baseKey}_{$dateKey}_[]",
                ];

                foreach ($cacheKeys as $cacheKey) {
                    Cache::forget($cacheKey);
                }
            }

            Log::info("Cache pattern cleared using fallback method for driver: " . config('cache.default'), [
                'pattern' => $pattern,
                'type' => $type,
                'user_id' => $userId,
                'cleared_keys_count' => count($dateRanges) * 4
            ]);
        } else {
            Log::warning("Could not parse cache pattern for fallback clearing: {$pattern}");
        }
    }

    /**
     * Warm up cache for user's analytics data
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return void
     */
    public function warmUpCache(User $user, Carbon $startDate, Carbon $endDate): void
    {
        try {
            $analyticsService = app(AnalyticsService::class);
            
            // Warm up common analytics queries
            $analyticsService->getDashboardSummary($user, $startDate, $endDate);
            
            Log::info("Analytics cache warmed up for user: {$user->id}");
        } catch (\Exception $e) {
            Log::error("Failed to warm up analytics cache for user: {$user->id}", [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get cache statistics
     *
     * @return array
     */
    public function getCacheStats(): array
    {
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $info = $redis->info();
                
                return [
                    'driver' => 'redis',
                    'used_memory' => $info['used_memory_human'] ?? 'N/A',
                    'connected_clients' => $info['connected_clients'] ?? 'N/A',
                    'keyspace_hits' => $info['keyspace_hits'] ?? 'N/A',
                    'keyspace_misses' => $info['keyspace_misses'] ?? 'N/A'
                ];
            }
            
            return [
                'driver' => config('cache.default'),
                'message' => 'Cache statistics not available for this driver'
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'Failed to retrieve cache statistics',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Check if analytics data should be cached based on date range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return bool
     */
    public function shouldCache(Carbon $startDate, Carbon $endDate): bool
    {
        $daysDiff = $startDate->diffInDays($endDate);
        
        // Don't cache very short date ranges (less than 1 day)
        if ($daysDiff < 1) {
            return false;
        }
        
        // Don't cache if end date is today (data might still be changing)
        if ($endDate->isToday()) {
            return false;
        }
        
        return true;
    }

    /**
     * Get appropriate cache TTL based on date range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param string $type
     * @return int
     */
    public function getCacheTtl(Carbon $startDate, Carbon $endDate, string $type): int
    {
        $daysDiff = $startDate->diffInDays($endDate);
        $baseTtl = self::CACHE_TTL[$type] ?? 3600;
        
        // Longer cache for historical data
        if ($endDate->lt(Carbon::now()->subDays(7))) {
            return $baseTtl * 4; // 4x longer for week-old data
        }
        
        // Shorter cache for recent data
        if ($endDate->isToday() || $endDate->isYesterday()) {
            return $baseTtl / 2; // Half the normal TTL
        }
        
        return $baseTtl;
    }
}
