<?php

namespace App\Services;

use App\Models\Accommodation;
use App\Models\AccommodationSearch;
use App\Models\Booking;
use App\Models\BookingStatus;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;


class AnalyticsService
{
    protected $cacheService;

    public function __construct(AnalyticsCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }
    /**
     * Get accommodation popularity ranking based on search volume
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds Optional filter by specific accommodations
     * @return array
     */
    public function getAccommodationPopularity(User $user, Carbon $startDate, Carbon $endDate, array $accommodationIds = []): array
    {
        // Ensure accommodationIds is always an array
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];

        $cacheKey = $this->cacheService->generateCacheKey('popularity', $user, $startDate, $endDate, $accommodationIds);

        return $this->cacheService->remember($cacheKey, 'popularity', function () use ($user, $startDate, $endDate, $accommodationIds) {
            $query = AccommodationSearch::select([
                'accommodation_id',
                DB::raw('COUNT(*) as search_count'),
                DB::raw('COUNT(CASE WHEN was_available = 1 THEN 1 END) as available_searches'),
                DB::raw('COUNT(CASE WHEN was_available = 0 THEN 1 END) as unavailable_searches'),
                DB::raw('AVG(quoted_price) as avg_quoted_price')
            ])
            ->whereHas('accommodation', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->whereBetween('searched_at', [$startDate, $endDate])
            ->groupBy('accommodation_id')
            ->orderBy('search_count', 'desc');

            if (!empty($accommodationIds)) {
                $query->whereIn('accommodation_id', $accommodationIds);
            }

            $results = $query->get();

            // Enrich with accommodation details
            return $results->map(function ($item) {
                $accommodation = Accommodation::find($item->accommodation_id);
                return [
                    'accommodation_id' => $item->accommodation_id,
                    'accommodation_name' => $accommodation->name ?? 'Unknown',
                    'search_count' => $item->search_count,
                    'available_searches' => $item->available_searches,
                    'unavailable_searches' => $item->unavailable_searches,
                    'availability_rate' => $item->search_count > 0 ? round(($item->available_searches / $item->search_count) * 100, 2) : 0,
                    'avg_quoted_price' => $item->avg_quoted_price ? round($item->avg_quoted_price, 2) : null
                ];
            })->toArray();
        });
    }

    /**
     * Get most requested date ranges and seasonal patterns
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds
     * @return array
     */
    public function getDateRangeAnalysis(User $user, Carbon $startDate, Carbon $endDate, array $accommodationIds = []): array
    {
        // Ensure accommodationIds is always an array
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];

        $cacheKey = $this->cacheService->generateCacheKey('date_analysis', $user, $startDate, $endDate, $accommodationIds);

        return $this->cacheService->remember($cacheKey, 'date_analysis', function () use ($user, $startDate, $endDate, $accommodationIds) {
            $baseQuery = AccommodationSearch::whereHas('accommodation', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->whereBetween('searched_at', [$startDate, $endDate]);

            if (!empty($accommodationIds)) {
                $baseQuery->whereIn('accommodation_id', $accommodationIds);
            }

            // Monthly analysis
            $monthlyData = (clone $baseQuery)
                ->select([
                    DB::raw('MONTH(start_date) as month'),
                    DB::raw('YEAR(start_date) as year'),
                    DB::raw('COUNT(*) as search_count'),
                    DB::raw('AVG(quoted_price) as avg_price')
                ])
                ->groupBy('month', 'year')
                ->orderBy('year')
                ->orderBy('month')
                ->get()
                ->map(function ($item) {
                    return [
                        'period' => Carbon::create($item->year, $item->month, 1)->format('M Y'),
                        'month' => $item->month,
                        'year' => $item->year,
                        'search_count' => $item->search_count,
                        'avg_price' => $item->avg_price ? round($item->avg_price, 2) : null
                    ];
                });

            // Day of week analysis
            $dayOfWeekData = (clone $baseQuery)
                ->select([
                    DB::raw('DAYOFWEEK(start_date) as day_of_week'),
                    DB::raw('COUNT(*) as search_count'),
                    DB::raw('AVG(quoted_price) as avg_price')
                ])
                ->groupBy('day_of_week')
                ->orderBy('day_of_week')
                ->get()
                ->map(function ($item) {
                    $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                    return [
                        'day' => $days[$item->day_of_week - 1] ?? 'Unknown',
                        'search_count' => $item->search_count,
                        'avg_price' => $item->avg_price ? round($item->avg_price, 2) : null
                    ];
                });

            // Stay duration analysis
            $durationData = (clone $baseQuery)
                ->select([
                    DB::raw('DATEDIFF(end_date, start_date) as stay_duration'),
                    DB::raw('COUNT(*) as search_count'),
                    DB::raw('AVG(quoted_price) as avg_price')
                ])
                ->groupBy('stay_duration')
                ->orderBy('stay_duration')
                ->get()
                ->map(function ($item) {
                    return [
                        'duration' => $item->stay_duration,
                        'search_count' => $item->search_count,
                        'avg_price' => $item->avg_price ? round($item->avg_price, 2) : null
                    ];
                });

            return [
                'monthly' => $monthlyData->toArray(),
                'day_of_week' => $dayOfWeekData->toArray(),
                'stay_duration' => $durationData->toArray()
            ];
        });
    }

    /**
     * Get top reasons for unavailability
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds
     * @return array
     */
    public function getUnavailabilityReasons(User $user, Carbon $startDate, Carbon $endDate, array $accommodationIds = []): array
    {
        // Ensure accommodationIds is always an array
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];

        $cacheKey = $this->cacheService->generateCacheKey('unavailability', $user, $startDate, $endDate, $accommodationIds);

        return $this->cacheService->remember($cacheKey, 'unavailability', function () use ($user, $startDate, $endDate, $accommodationIds) {
            // First get the total count of unavailable searches for percentage calculation
            $totalUnavailableQuery = AccommodationSearch::whereHas('accommodation', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->where('was_available', false)
            ->whereBetween('searched_at', [$startDate, $endDate]);

            if (!empty($accommodationIds)) {
                $totalUnavailableQuery->whereIn('accommodation_id', $accommodationIds);
            }

            $totalUnavailable = $totalUnavailableQuery->count();

            // Now get the breakdown by reason
            $query = AccommodationSearch::select([
                'unavailability_reason',
                DB::raw('COUNT(*) as count')
            ])
            ->whereHas('accommodation', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->where('was_available', false)
            ->whereNotNull('unavailability_reason')
            ->whereBetween('searched_at', [$startDate, $endDate]);

            if (!empty($accommodationIds)) {
                $query->whereIn('accommodation_id', $accommodationIds);
            }

            return $query->groupBy('unavailability_reason')
                ->orderBy('count', 'desc')
                ->get()
                ->map(function ($item) use ($totalUnavailable) {
                    $percentage = $totalUnavailable > 0 ? round(($item->count / $totalUnavailable) * 100, 2) : 0;
                    return [
                        'reason' => $item->unavailability_reason,
                        'count' => $item->count,
                        'percentage' => $percentage
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Calculate search-to-booking conversion ratio
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds
     * @return array
     */
    public function getConversionRatio(User $user, Carbon $startDate, Carbon $endDate, array $accommodationIds = []): array
    {
        // Ensure accommodationIds is always an array
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];

        $cacheKey = $this->cacheService->generateCacheKey('conversion', $user, $startDate, $endDate, $accommodationIds);

        return $this->cacheService->remember($cacheKey, 'conversion', function () use ($user, $startDate, $endDate, $accommodationIds) {
            // Get confirmed booking status ID
            $confirmedStatusId = BookingStatus::where('name', 'Confirmed')->first()?->id;
            
            $accommodationQuery = Accommodation::where('user_id', $user->id);
            if (!empty($accommodationIds)) {
                $accommodationQuery->whereIn('id', $accommodationIds);
            }
            $userAccommodationIds = $accommodationQuery->pluck('id')->toArray();

            // Count total searches
            $totalSearches = AccommodationSearch::whereIn('accommodation_id', $userAccommodationIds)
                ->whereBetween('searched_at', [$startDate, $endDate])
                ->count();

            // Count available searches
            $availableSearches = AccommodationSearch::whereIn('accommodation_id', $userAccommodationIds)
                ->where('was_available', true)
                ->whereBetween('searched_at', [$startDate, $endDate])
                ->count();

            // Count confirmed bookings in the same period
            $confirmedBookings = 0;
            if ($confirmedStatusId) {
                $confirmedBookings = Booking::whereIn('accommodation_id', $userAccommodationIds)
                    ->where('booking_status_id', $confirmedStatusId)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count();
            }

            $conversionRate = $availableSearches > 0 ? round(($confirmedBookings / $availableSearches) * 100, 2) : 0;
            $availabilityRate = $totalSearches > 0 ? round(($availableSearches / $totalSearches) * 100, 2) : 0;

            return [
                'total_searches' => $totalSearches,
                'available_searches' => $availableSearches,
                'confirmed_bookings' => $confirmedBookings,
                'conversion_rate' => $conversionRate,
                'availability_rate' => $availabilityRate
            ];
        });
    }

    /**
     * Get search volume trends over time
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds
     * @param string $interval (daily, weekly, monthly)
     * @return array
     */
    public function getSearchTrends(User $user, Carbon $startDate, Carbon $endDate, array $accommodationIds = [], string $interval = 'daily'): array
    {
        // Ensure accommodationIds is always an array
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];

        $cacheKey = $this->cacheService->generateCacheKey('trends', $user, $startDate, $endDate, $accommodationIds, ['interval' => $interval]);

        return $this->cacheService->remember($cacheKey, 'trends', function () use ($user, $startDate, $endDate, $accommodationIds, $interval) {
            $dateFormat = match($interval) {
                'weekly' => '%Y-%u',
                'monthly' => '%Y-%m',
                default => '%Y-%m-%d'
            };

            $query = AccommodationSearch::select([
                DB::raw("DATE_FORMAT(searched_at, '{$dateFormat}') as period"),
                DB::raw('COUNT(*) as search_count'),
                DB::raw('COUNT(CASE WHEN was_available = 1 THEN 1 END) as available_count'),
                DB::raw('AVG(quoted_price) as avg_price')
            ])
            ->whereHas('accommodation', function ($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->whereBetween('searched_at', [$startDate, $endDate]);

            if (!empty($accommodationIds)) {
                $query->whereIn('accommodation_id', $accommodationIds);
            }

            return $query->groupBy('period')
                ->orderBy('period')
                ->get()
                ->map(function ($item) use ($interval) {
                    return [
                        'period' => $item->period,
                        'search_count' => $item->search_count,
                        'available_count' => $item->available_count,
                        'availability_rate' => $item->search_count > 0 ? round(($item->available_count / $item->search_count) * 100, 2) : 0,
                        'avg_price' => $item->avg_price ? round($item->avg_price, 2) : null
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get comprehensive dashboard summary
     *
     * @param User $user
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param array $accommodationIds
     * @return array
     */
    public function getDashboardSummary(User $user, Carbon $startDate, Carbon $endDate, array $accommodationIds = []): array
    {
        // Ensure accommodationIds is always an array
        $accommodationIds = is_array($accommodationIds) ? $accommodationIds : [];

        return [
            'popularity' => $this->getAccommodationPopularity($user, $startDate, $endDate, $accommodationIds),
            'date_analysis' => $this->getDateRangeAnalysis($user, $startDate, $endDate, $accommodationIds),
            'unavailability_reasons' => $this->getUnavailabilityReasons($user, $startDate, $endDate, $accommodationIds),
            'conversion_metrics' => $this->getConversionRatio($user, $startDate, $endDate, $accommodationIds),
            'search_trends' => $this->getSearchTrends($user, $startDate, $endDate, $accommodationIds, 'daily')
        ];
    }

    /**
     * Clear analytics cache for a user
     *
     * @param User $user
     * @return void
     */
    public function clearUserCache(User $user): void
    {
        $patterns = [
            "analytics_popularity_{$user->id}_*",
            "analytics_dates_{$user->id}_*",
            "analytics_unavailability_{$user->id}_*",
            "analytics_conversion_{$user->id}_*",
            "analytics_trends_{$user->id}_*"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }
}
