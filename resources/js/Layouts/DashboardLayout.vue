<script setup>
import { ref, onMounted, watch, nextTick, computed } from 'vue';
import { Head, Link, router, usePage } from '@inertiajs/vue3';
import ApplicationMark from '@/Components/ApplicationMark.vue';
import Banner from '@/Components/Banner.vue';
import NotificationDropdown from '@/Components/NotificationDropdown.vue';
import axios from 'axios';
import { FeedbackFish } from "@feedback-fish/vue";

defineProps({
    title: String,
    hideSidebar: {
        type: Boolean,
        default: false
    },
});

const sidebarOpen = ref(true);
const darkMode = ref(false);
const page = usePage();
const notifications = ref([]);
const unreadNotificationsCount = ref(0);

// Fetch notifications directly from the server
const fetchNotifications = async () => {
    try {
        // Use axios to fetch notifications
        const response = await axios.get(route('notifications.api.get'), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        });

        if (response.data) {
            notifications.value = response.data.notifications || [];
            unreadNotificationsCount.value = response.data.unreadCount || 0;
        }
    } catch (error) {
        console.error('Error fetching notifications:', error);
    }
};

// Get the user's current plan
const userPlan = computed(() => {
    if (page && page.props && page.props.auth && page.props.auth.user && page.props.auth.user.subscription) {
        return page.props.auth.user.subscription;
    }
    return null;
});

const logout = () => {
    router.post(route('logout'));
};

const toggleDarkMode = () => {
    darkMode.value = !darkMode.value;
    if (darkMode.value) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('darkMode', 'true');
    } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('darkMode', 'false');
    }
};

// Initialize dark mode and Preline components
onMounted(() => {
    // Check for dark mode preference in localStorage
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode === 'true') {
        darkMode.value = true;
        document.documentElement.classList.add('dark');
    } else if (savedDarkMode === null && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        // If no preference is saved, check system preference
        darkMode.value = true;
        document.documentElement.classList.add('dark');
    }

    // Call the Preline initialization function
    if (typeof window !== 'undefined' && window.HSStaticMethods) {
        window.HSStaticMethods.autoInit();
    }

    // Fetch notifications
    fetchNotifications();
});

// Re-initialize Preline components when route changes
watch(() => {
    // Check if router and currentRoute exist before accessing properties
    if (router && router.currentRoute && router.currentRoute.value) {
        return router.currentRoute.value.fullPath;
    }
    return null;
}, () => {
    nextTick(() => {
        if (typeof window !== 'undefined' && window.HSStaticMethods) {
            window.HSStaticMethods.autoInit();
        }
    });
});
</script>

<template>
    <div>
        <Head :title="title" />

        <Banner />

        <!-- ========== MAIN CONTENT ========== -->
        <div class="flex bg-background dark:bg-gray-900">
            <!-- Sidebar Backdrop (mobile only) -->
            <div
                v-if="!hideSidebar"
                v-show="sidebarOpen"
                class="fixed inset-0 z-[50] bg-gray-900 bg-opacity-50 dark:bg-opacity-50 lg:hidden"
                @click="sidebarOpen = false"
            ></div>

            <!-- Sidebar -->
            <div v-if="!hideSidebar" id="application-sidebar"
                 :class="[sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
                         'transition-all duration-300 transform fixed inset-y-0 start-0 z-[60] w-64 bg-primary dark:bg-gray-800 border-e border-gray-200 dark:border-gray-700 pt-7 pb-10 overflow-y-auto lg:block lg:translate-x-0 lg:end-auto lg:bottom-0']">
                <div class="px-6 flex items-center justify-between">
                    <Link :href="route('dashboard')" class="flex items-center gap-x-3 text-xl font-semibold text-white w-full">
                        <ApplicationMark class="block w-full" :beta="true" />
                    </Link>

                    <!-- Close button (mobile only) -->
                    <button
                        type="button"
                        class="lg:hidden text-white hover:text-gray-200 p-2"
                        @click="sidebarOpen = false"
                    >
                        <span class="sr-only">Close sidebar</span>
                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>

                <nav class="p-6 w-full flex flex-col flex-wrap">
                    <ul class="space-y-1.5">
                        <li>
                            <Link :href="route('dashboard')"
                                  :class="[route().current('dashboard') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                                </svg>
                                Dashboard
                            </Link>
                        </li>

						<!-- Accommodation Groups -->
						 <li>
							<Link :href="route('accommodation-groups.index')"
									:class="[route().current('accommodation-groups.*') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
											'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
								<svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
									<path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
								</svg>
								Accommodation Groups
							</Link>
						</li>

						<li>
							<Link :href="route('accommodations.index')"
									:class="[route().current('accommodations.index') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
											'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
								<svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                                    <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9"></path>
                                    <path d="M12 3v6"></path>
                                </svg>
								Accommodations
							</Link>
						</li>

                        <li>
                            <Link :href="route('bookings.index')"
                                  :class="[route().current('bookings.*') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M8 2v4"></path>
                                    <path d="M16 2v4"></path>
                                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                    <path d="M3 10h18"></path>
                                    <path d="m8 14 2 2 4-4"></path>
                                </svg>
                                Bookings
                            </Link>
                        </li>

                        <li>
                            <Link :href="route('sites.index')"
                                  :class="[route().current('sites.*') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                    <line x1="8" y1="21" x2="16" y2="21"></line>
                                    <line x1="12" y1="17" x2="12" y2="21"></line>
                                </svg>
                                Site Embeds
                            </Link>
                        </li>

                        <li>
                            <Link :href="route('analytics')"
                                  :class="[route().current('analytics') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 3v18h18"></path>
                                    <path d="m19 9-5 5-4-4-3 3"></path>
                                </svg>
                                Analytics
                            </Link>
                        </li>

                        <li class="pt-3">
                            <div class="py-2 px-3 text-xs font-medium uppercase text-gray-400">
                                Account
                            </div>
                        </li>

                        <!-- Current Plan -->
                        <li v-if="userPlan" class="mb-3">
                            <div class="flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg bg-accent-primary/10">
                                <svg class="flex-shrink-0 w-4 h-4 text-accent-primary" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 13c0 5-3.5 7.5-7.66 7.5h-.68c-4.16 0-7.66-2.5-7.66-7.5V4.5A1.5 1.5 0 0 1 5.5 3h13a1.5 1.5 0 0 1 1.5 1.5Z"/>
                                    <path d="M15 3v4.5a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 7.5V3"/>
                                    <path d="M9 11h6"/>
                                    <path d="M9 14h6"/>
                                </svg>
                                <div>
                                    <span class="block text-xs text-gray-500">Current Plan</span>
                                    <span class="block font-medium text-white">{{ userPlan.plan.name }}</span>
                                </div>
                                <Link :href="route('plans.index')" class="ml-auto text-xs text-accent-primary hover:underline">
                                    Change
                                </Link>
                            </div>
                        </li>

                        <li>
                            <Link :href="route('account.index')"
                                  :class="[route().current('account.*') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M4 10c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2"></path>
                                    <path d="M10 16c-1.1 0-2-.9-2-2v-4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2"></path>
                                    <rect width="8" height="8" x="14" y="14" rx="2"></rect>
                                </svg>
                                Account
                            </Link>
                        </li>

                        <li>
                            <Link :href="route('profile.show')"
                                  :class="[route().current('profile.show') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                Profile
                            </Link>
                        </li>

                        <li>
                            <Link :href="route('settings.index')"
                                  :class="[route().current('settings.*') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg>
                                Settings
                            </Link>
                        </li>

                        <!-- <li v-if="$page.props.jetstream && $page.props.jetstream.hasApiFeatures">
                            <Link :href="route('api-tokens.index')"
                                  :class="[route().current('api-tokens.index') ? 'bg-accent-primary text-white' : 'text-gray-200 hover:bg-accent-primary/80 hover:text-white',
                                          'flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg']">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 2a10 10 0 1 0 10 10H12V2Z"></path>
                                    <path d="M21.2 8A10 10 0 0 0 15 2.8"></path>
                                    <path d="M12 9h4"></path>
                                    <path d="M12 12h3"></path>
                                    <path d="M12 15h2"></path>
                                </svg>
                                API Tokens
                            </Link>
                        </li> -->

                        <!-- Teams feature hidden as requested -->
                        <!-- <li v-if="$page.props.jetstream && $page.props.jetstream.hasTeamFeatures && $page.props.auth && $page.props.auth.user && $page.props.auth.user.all_teams && $page.props.auth.user.all_teams.length > 0">
                            <button type="button" class="w-full text-start flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-200 rounded-lg hover:bg-accent-primary/80 hover:text-white" data-hs-collapse="#teams-collapse" @click="toggleTeamsDropdown">
                                <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                                Teams
                                <svg class="hs-collapse-open:rotate-180 ms-auto w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6"></path>
                                </svg>
                            </button>

                            <div id="teams-collapse" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300">
                                <ul class="pt-2 ps-2">
                                    <li class="pb-1">
                                        <div class="py-2 px-3 text-xs font-medium uppercase text-gray-400">
                                            Manage Teams
                                        </div>
                                    </li>

                                    <li>
                                        <Link v-if="$page.props.auth && $page.props.auth.user && $page.props.auth.user.current_team" :href="route('teams.show', $page.props.auth.user.current_team)" class="flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-200 rounded-lg hover:bg-accent-primary/80 hover:text-white">
                                            <svg class="h-5 w-5 text-accent-secondary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            <span class="truncate">{{ $page.props.auth.user.current_team.name }} Settings</span>
                                        </Link>
                                    </li>

                                    <li v-if="$page.props.jetstream && $page.props.jetstream.canCreateTeams">
                                        <Link :href="route('teams.create')" class="flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-200 rounded-lg hover:bg-accent-primary/80 hover:text-white">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                                            </svg>
                                            <span class="truncate">Create New Team</span>
                                        </Link>
                                    </li>

                                    <li v-if="$page.props.auth && $page.props.auth.user && $page.props.auth.user.all_teams && $page.props.auth.user.all_teams.length > 1" class="pt-3">
                                        <div class="py-2 px-3 text-xs font-medium uppercase text-gray-400">
                                            Switch Teams
                                        </div>

                                        <div v-for="team in $page.props.auth.user.all_teams" :key="team.id">
                                            <div class="flex items-center">
                                                <form @submit.prevent="switchToTeam(team)">
                                                    <button type="submit" class="w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-200 rounded-lg hover:bg-accent-primary/80 hover:text-white">
                                                        <svg v-if="$page.props.auth && $page.props.auth.user && $page.props.auth.user.current_team_id && team.id == $page.props.auth.user.current_team_id" class="h-5 w-5 text-accent-secondary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                        <span class="truncate">{{ team.name }}</span>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li> -->

                        <li class="pt-3">
                            <div class="py-2 px-3 text-xs font-medium uppercase text-gray-400">
                                Actions
                            </div>
                        </li>

						<li>	
							<FeedbackFish projectId="ccbf1443161c97" />
							<a data-feedback-fish href="#" class="text-gray-200 hover:bg-accent-primary/80 hover:text-white flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg">
								<svg class="flex-shrink-0 w-4 h-4" clip-rule="evenodd" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 24 24"
									xmlns="http://www.w3.org/2000/svg">
									<path fill="white" d="m12.002 21.534c5.518 0 9.998-4.48 9.998-9.998s-4.48-9.997-9.998-9.997c-5.517 0-9.997 4.479-9.997 9.997s4.48 9.998 9.997 9.998zm0-1.5c-4.69 0-8.497-3.808-8.497-8.498s3.807-8.497 8.497-8.497 8.498 3.807 8.498 8.497-3.808 8.498-8.498 8.498zm0-6.5c-.414 0-.75-.336-.75-.75v-5.5c0-.414.336-.75.75-.75s.75.336.75.75v5.5c0 .414-.336.75-.75.75zm-.002 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z" fill-rule="nonzero"/>
								</svg>
								Feedback
							</a>
						</li>


                        <li>
                            <form @submit.prevent="logout">
                                <button type="submit" class="w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-200 rounded-lg hover:bg-accent-primary/80 hover:text-white">
                                    <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                        <polyline points="16 17 21 12 16 7"></polyline>
                                        <line x1="21" y1="12" x2="9" y2="12"></line>
                                    </svg>
                                    Log Out
                                </button>
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>
            <!-- End Sidebar -->

            <!-- Content -->
            <div :class="['w-full', !hideSidebar ? 'lg:ps-64' : '']">
                <!-- Header -->
                <header class="sticky top-0 inset-x-0 flex flex-wrap sm:justify-start sm:flex-nowrap z-[48] w-full bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 text-sm py-2.5 sm:py-4">
                    <nav class="flex basis-full items-center justify-end w-full mx-auto px-4 sm:px-6 md:px-8" aria-label="Global">
                        <div class="me-5 lg:me-0 lg:hidden">
                            <button type="button" class="text-primary dark:text-white hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-md" @click="sidebarOpen = !sidebarOpen">
                                <span class="sr-only">Toggle navigation</span>
                                <svg class="flex-shrink-0 w-5 h-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="3" x2="21" y1="6" y2="6"></line>
                                    <line x1="3" x2="21" y1="12" y2="12"></line>
                                    <line x1="3" x2="21" y1="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>

                        <div class="flex items-center justify-end ms-auto sm:justify-between sm:gap-x-3 sm:order-3">


                            <div class="flex flex-row items-center justify-end gap-2">
                                <!-- Dark Mode Toggle -->
                                <button
                                    type="button"
                                    class="inline-flex flex-shrink-0 justify-center items-center h-9 w-9 rounded-full text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white transition-all text-xs dark:focus:ring-gray-700 dark:focus:ring-offset-gray-800"
                                    @click="toggleDarkMode"
                                >
                                    <svg v-if="!darkMode" class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/>
                                    </svg>
                                    <svg v-else class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="4"/>
                                        <path d="M12 2v2"/>
                                        <path d="M12 20v2"/>
                                        <path d="m4.93 4.93 1.41 1.41"/>
                                        <path d="m17.66 17.66 1.41 1.41"/>
                                        <path d="M2 12h2"/>
                                        <path d="M20 12h2"/>
                                        <path d="m6.34 17.66-1.41 1.41"/>
                                        <path d="m19.07 4.93-1.41 1.41"/>
                                    </svg>
                                </button>

                                <!-- Notifications -->
                                <NotificationDropdown
                                    :notifications="notifications"
                                    :unread-count="unreadNotificationsCount"
                                    class="ml-2"
                                />

                                <!-- Teams Dropdown (Hidden as requested) -->
                                <!-- <div v-if="$page.props.jetstream && $page.props.jetstream.hasTeamFeatures" class="ms-3 relative">
                                    <div class="hs-dropdown relative inline-flex">
                                        <button id="hs-dropdown-custom-trigger" type="button" class="hs-dropdown-toggle py-1 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-full border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                                            <span class="text-primary">{{ $page.props.auth && $page.props.auth.user && $page.props.auth.user.current_team ? $page.props.auth.user.current_team.name : 'Team' }}</span>
                                            <svg class="hs-dropdown-open:rotate-180 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>

                                        <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg p-2 mt-2 divide-y divide-gray-200 z-50" aria-labelledby="hs-dropdown-custom-trigger">
                                            <div class="py-2 first:pt-0 last:pb-0">
                                                <span class="block py-2 px-3 text-xs font-medium uppercase text-gray-400">
                                                    Manage Team
                                                </span>

                                                <Link v-if="$page.props.auth && $page.props.auth.user && $page.props.auth.user.current_team" :href="route('teams.show', $page.props.auth.user.current_team)" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100">
                                                    Team Settings
                                                </Link>

                                                <Link v-if="$page.props.jetstream && $page.props.jetstream.canCreateTeams" :href="route('teams.create')" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100">
                                                    Create New Team
                                                </Link>
                                            </div>

                                            <div v-if="$page.props.auth && $page.props.auth.user && $page.props.auth.user.all_teams && $page.props.auth.user.all_teams.length > 1" class="py-2 first:pt-0 last:pb-0">
                                                <span class="block py-2 px-3 text-xs font-medium uppercase text-gray-400">
                                                    Switch Teams
                                                </span>

                                                <div v-for="team in $page.props.auth.user.all_teams" :key="team.id">
                                                    <form @submit.prevent="switchToTeam(team)">
                                                        <button type="submit" class="w-full flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100">
                                                            <svg v-if="$page.props.auth && $page.props.auth.user && $page.props.auth.user.current_team_id && team.id == $page.props.auth.user.current_team_id" class="h-5 w-5 text-accent-secondary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            <span class="truncate">{{ team.name }}</span>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->

                                <!-- User Dropdown -->
                                <div class="hs-dropdown relative inline-flex">
                                    <button id="hs-dropdown-with-header" type="button" class="hs-dropdown-toggle inline-flex flex-shrink-0 justify-center items-center gap-2 h-[2.375rem] w-[2.375rem] rounded-full font-medium bg-white text-gray-700 align-middle hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white transition-all text-xs">
                                        <img
                                            class="inline-block h-[2.375rem] w-[2.375rem] rounded-full object-cover"
                                            :src="($page.props.auth && $page.props.auth.user && $page.props.auth.user.profile_photo_url) ? $page.props.auth.user.profile_photo_url : 'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&w=300&h=300&q=80'"
                                            :alt="($page.props.auth && $page.props.auth.user && $page.props.auth.user.name) ? $page.props.auth.user.name : 'User Profile'"
                                        >
                                    </button>

                                    <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white dark:bg-gray-800 shadow-md rounded-lg p-2 mt-2 divide-y divide-gray-200 dark:divide-gray-700 z-50" aria-labelledby="hs-dropdown-with-header">
                                        <div class="py-3 px-5 -m-2 bg-gray-100 dark:bg-gray-700 rounded-t-lg">
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Signed in as</p>
                                            <p class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ $page.props.auth && $page.props.auth.user ? $page.props.auth.user.email : 'User' }}</p>
                                        </div>
                                        <div class="py-2 first:pt-0 last:pb-0">
                                            <Link :href="route('profile.show')" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700">
                                                Profile
                                            </Link>
                                            <!-- <Link v-if="$page.props.jetstream && $page.props.jetstream.hasApiFeatures" :href="route('api-tokens.index')" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700">
                                                API Tokens
                                            </Link> -->
                                            <form @submit.prevent="logout">
                                                <button type="submit" class="w-full flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700">
                                                    Log Out
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </nav>
                </header>
                <!-- End Header -->

                <!-- Page Heading -->
                <header v-if="$slots.header" class="bg-white dark:bg-gray-800 shadow">
                    <div class="py-6 px-4 sm:px-6 lg:px-8">
                        <slot name="header" />
                    </div>
                </header>

                <!-- Page Content -->
                <main class="bg-background dark:bg-gray-900 min-h-screen">
                    <slot />
                </main>
            </div>
        </div>
        <!-- ========== END MAIN CONTENT ========== -->
    </div>
</template>
