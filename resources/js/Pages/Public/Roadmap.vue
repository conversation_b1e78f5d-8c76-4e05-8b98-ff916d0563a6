<script setup>
import { Link } from '@inertiajs/vue3';
import SeoMeta from '@/Components/SeoMeta.vue';
import PublicLayout from '@/Layouts/PublicLayout.vue';
import { FeedbackFish } from "@feedback-fish/vue";

import {
	CheckCircle as CheckCircleIcon,
	Clock as ClockIcon,
	CalendarClock as CalendarClockIcon,
	Lightbulb as LightbulbIcon,
	Rocket as RocketIcon,
	Sparkles as SparklesIcon
} from 'lucide-vue-next'

// Roadmap data
const roadmapSections = [
	{
		title: "Completed Features",
		icon: CheckCircleIcon,
		iconClass: "text-green-500",
		items: [
			{
				title: "Core Booking Management",
				description: "Create and manage accommodations with detailed descriptions, pricing, and availability settings."
			},
			{
				title: "Accommodation Groups",
				description: "Group accommodations with shared settings and pricing rules for easier management."
			},
			{
				title: "Flexible Pricing Options",
				description: "Set pricing based on seasons, days of the week, occupancy, and more."
			},
			{
				title: "Group-Level Pricing",
				description: "Apply pricing rules at the group level with the option to override at the individual accommodation level."
			},
			{
				title: "Booking Widget",
				description: "Embeddable widget for your website that lets visitors check availability and request bookings."
			},
			{
				title: "Booking Notifications",
				description: "Email and in-app notifications for new bookings and status updates."
			},
			{
				title: "Availability Management",
				description: "Manage accommodation availability by setting recurring unavailable days or unavailability periods."
			},
			{
				title: "Advanced Analytics Dashboard",
				description: "Insights into booking trends, occupancy rates, and revenue metrics to help you grow."
			},
			{
				title: "Subscription Management",
				description: "Flexible pricing tiers with different feature sets and accommodation limits."
			},
		]
	},
	{
		title: "In Progress",
		icon: ClockIcon,
		iconClass: "text-amber-500",
		items: [
			{
				title: "WordPress Plugin",
				description: "A native plugin to easily integrate Booking Bear into your WordPress site."
			},
			{
				title: "API for Developers",
				description: "RESTful API for advanced custom integrations and third-party tools."
			}
		]
	},
	{
		title: "Coming Soon",
		icon: CalendarClockIcon,
		iconClass: "text-blue-500",
		items: [
			
			{
				title: "Enhanced Widget Customization",
				description: "More control over the appearance and behavior of the widget for seamless site integration."
			},
			{
				title: "Group Based Availability",
				description: "Apply group based availability rules to accommodations."
			}
		]
	},
	{
		title: "Future Vision",
		icon: LightbulbIcon,
		iconClass: "text-purple-500",
		items: [
			{
				title: "Payment Processing Integration",
				description: "Allow guests to pay for their bookings online."
			},
			{
				title: "Smart Search Suggestions",
				description: "Use intelligent suggestions to offer alternative dates or accommodations when something is unavailable."
			},
			{
				title: "Team Collaboration Tools",
				description: "Invite team members to help manage bookings and accommodations with custom permissions."
			}
		]
	}
]

const pageSchema = {
  '@context': 'https://schema.org',
  '@type': 'ItemList',
  name: 'BookingBear Product Roadmap',
  description: 'Explore the past, current, and planned features for BookingBear booking management platform.',
  itemListElement: roadmapSections.flatMap((section, sectionIndex) => 
    section.items.map((item, itemIndex) => ({
      '@type': 'ListItem',
      position: sectionIndex * 10 + itemIndex + 1,
      item: {
        '@type': 'SoftwareApplication',
        name: item.title,
        description: item.description,
        applicationCategory: 'BusinessApplication',
        operatingSystem: 'Web',
        dateCreated: item.date || '2025-05-20',
        featureList: item.description,
        status: section.title === 'Completed Features' ? 'Completed' : 
               section.title === 'In Progress' ? 'InProgress' : 'Planned'
      }
    }))
  )
};
</script>

<template>
  <PublicLayout title="Product Roadmap | BookingBear Feature Development">
    <SeoMeta 
      title="Product Roadmap | BookingBear Feature Development"
      description="Explore BookingBear's product roadmap - from core booking features to upcoming integrations. See how we're building the future of accommodation management."
      :schema="pageSchema"
    />
    
		<!-- Hero Section -->
		<section class="py-16 md:py-24 px-4 bg-white">
			<div class="container mx-auto max-w-7xl">
				<div class="text-center mb-16">
					<h1 class="text-4xl md:text-5xl font-bold text-primary mb-6">
						Product Roadmap
					</h1>
					<p class="text-xl text-gray-700 max-w-3xl mx-auto">
						Our journey to build the best accommodation booking management platform. See what we've
						accomplished and what's coming next.
					</p>
				</div>

				<!-- Roadmap Timeline -->
				<div class="relative">
					<!-- Vertical line -->
					<div
						class="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gray-200">
					</div>

					<!-- Roadmap Sections -->
					<div class="space-y-12 md:space-y-24 relative">
						<div v-for="(section, sectionIndex) in roadmapSections" :key="sectionIndex"
							class="relative">
							<!-- Section Header -->
							<div class="flex items-center justify-center mb-8">
								<div :class="[
									'flex items-center justify-center w-16 h-16 rounded-full z-10',
									section.iconClass ? 'bg-white border-2 border-current' : 'bg-primary text-white'
								]">
									<component :is="section.icon" :class="[section.iconClass, 'w-8 h-8']" />
								</div>
							</div>

							<h2 class="text-2xl md:text-3xl font-bold text-primary text-center mb-8 bg-white">
								{{ section.title }}
							</h2>

							<!-- Items Grid -->
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
								<div v-for="(item, itemIndex) in section.items" :key="itemIndex"
									class="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-100">
									<h3 class="text-xl font-semibold text-primary mb-3">
										{{ item.title }}
									</h3>
									<p class="text-gray-700">{{ item.description }}</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Call to Action -->
				<div class="mt-16 md:mt-24 text-center">
					<div class="bg-primary text-white p-8 md:p-12 rounded-xl">
						<RocketIcon class="w-12 h-12 mx-auto mb-6" />
						<h2 class="text-2xl md:text-3xl font-bold mb-4">Help Shape Our Future</h2>
						<p class="text-lg mb-8 max-w-2xl mx-auto">
							We're constantly improving Booking Bear based on user feedback. Have a feature
							suggestion or idea? We'd love to hear from you!
						</p>
						<FeedbackFish projectId="ccbf1443161c97" />
						<a data-feedback-fish href="#" class="inline-flex items-center bg-white text-primary px-6 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors">
							<SparklesIcon class="w-5 h-5 mr-2" />
							Share Your Ideas
						</a>
					</div>
				</div>
			</div>
		</section>
  </PublicLayout>
</template>
