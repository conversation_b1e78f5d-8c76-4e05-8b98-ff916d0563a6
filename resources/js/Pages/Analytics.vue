<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Head } from '@inertiajs/vue3'
import DashboardLayout from '@/Layouts/DashboardLayout.vue'
import Card from '@/Components/Card.vue'
import PrimaryButton from '@/Components/PrimaryButton.vue'
import SecondaryButton from '@/Components/SecondaryButton.vue'
import MetricCard from '@/Components/Analytics/MetricCard.vue'
import BarChart from '@/Components/Charts/BarChart.vue'
import PieChart from '@/Components/Charts/PieChart.vue'
import TimeSeriesChart from '@/Components/Charts/TimeSeriesChart.vue'
import { Calendar, Download, Filter, TrendingUp, Users, DollarSign, AlertCircle, Target, Lightbulb } from 'lucide-vue-next'
import axios from 'axios'

const props = defineProps({
  accommodations: {
    type: Array,
    default: () => []
  },
  accommodationGroups: {
    type: Array,
    default: () => []
  }
})

// Reactive data
const loading = ref(false)
const analyticsData = ref(null)
const insights = ref([])
const error = ref(null)

// Filter controls
const selectedPeriod = ref('30')
const customStartDate = ref('')
const customEndDate = ref('')
const selectedAccommodations = ref([])
const selectedGroups = ref([])
const showFilters = ref(false)

// Period options
const periodOptions = [
  { value: '7', label: 'Last 7 days' },
  { value: '30', label: 'Last 30 days' },
  { value: '90', label: 'Last 90 days' },
  { value: 'custom', label: 'Custom range' }
]

// Computed properties
const isCustomPeriod = computed(() => selectedPeriod.value === 'custom')

const filteredAccommodations = computed(() => {
  if (selectedGroups.value.length === 0) {
    return props.accommodations
  }
  return props.accommodations.filter(acc => 
    selectedGroups.value.includes(acc.accommodation_group_id)
  )
})

const selectedAccommodationIds = computed(() => {
  if (selectedAccommodations.value.length === 0) {
    return filteredAccommodations.value.map(acc => acc.id)
  }
  return selectedAccommodations.value
})

// Methods
const fetchAnalyticsData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const params = {
      period: selectedPeriod.value,
      accommodation_ids: selectedAccommodationIds.value
    }
    
    if (isCustomPeriod.value) {
      params.start_date = customStartDate.value
      params.end_date = customEndDate.value
    }
    
    const response = await axios.get('/api/analytics/dashboard', { params })
    analyticsData.value = response.data.data
    insights.value = response.data.insights || []
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to load analytics data'
    console.error('Analytics error:', err)
  } finally {
    loading.value = false
  }
}

const exportData = async (type) => {
  try {
    const params = {
      type,
      accommodation_ids: selectedAccommodationIds.value
    }

    // Convert period to start_date and end_date (same logic as dashboard API)
    if (isCustomPeriod.value) {
      params.start_date = customStartDate.value
      params.end_date = customEndDate.value
    } else {
      const days = parseInt(selectedPeriod.value)
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - days)

      params.start_date = startDate.toISOString().split('T')[0]
      params.end_date = endDate.toISOString().split('T')[0]
    }

    const response = await axios.get('/api/analytics/export-csv', {
      params,
      responseType: 'blob'
    })

    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `analytics_${type}_${params.start_date}_to_${params.end_date}.csv`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  } catch (err) {
    console.error('Export error:', err)
    alert('Failed to export data')
  }
}

const resetFilters = () => {
  selectedPeriod.value = '30'
  customStartDate.value = ''
  customEndDate.value = ''
  selectedAccommodations.value = []
  selectedGroups.value = []
}

// Watchers with debouncing
let watcherTimeout = null

watch([selectedPeriod, customStartDate, customEndDate, selectedAccommodationIds], () => {
  // Clear any pending updates
  if (watcherTimeout) {
    clearTimeout(watcherTimeout)
  }

  // Debounce the analytics fetch
  watcherTimeout = setTimeout(() => {
    if (selectedPeriod.value !== 'custom' || (customStartDate.value && customEndDate.value)) {
      fetchAnalyticsData()
    }
  }, 300) // 300ms debounce
}, { flush: 'post' })

// Lifecycle
onMounted(() => {
  fetchAnalyticsData()
})

onUnmounted(() => {
  // Clean up any pending timeouts
  if (watcherTimeout) {
    clearTimeout(watcherTimeout)
  }
})

// Chart data computed properties
const popularityChartData = computed(() => {
  if (!analyticsData.value?.popularity) return { labels: [], datasets: [] }
  
  const data = analyticsData.value.popularity.slice(0, 10) // Top 10
  return {
    labels: data.map(item => item.accommodation_name),
    datasets: [{
      label: 'Search Count',
      data: data.map(item => item.search_count),
      backgroundColor: '#f97316'
    }]
  }
})

const conversionMetrics = computed(() => {
  if (!analyticsData.value?.conversion_metrics) return null
  return analyticsData.value.conversion_metrics
})

const unavailabilityReasonsData = computed(() => {
  if (!analyticsData.value?.unavailability_reasons) return { labels: [], data: [] }
  
  const data = analyticsData.value.unavailability_reasons
  return {
    labels: data.map(item => item.reason),
    data: data.map(item => item.count)
  }
})

const searchTrendsData = computed(() => {
  if (!analyticsData.value?.search_trends) return { datasets: [] }
  
  const trends = analyticsData.value.search_trends
  return {
    datasets: [{
      label: 'Search Volume',
      data: trends.map(item => ({
        x: item.period,
        y: item.search_count
      })),
      borderColor: '#06b6d4',
      backgroundColor: '#06b6d420'
    }, {
      label: 'Available Searches',
      data: trends.map(item => ({
        x: item.period,
        y: item.available_count
      })),
      borderColor: '#10b981',
      backgroundColor: '#10b98120'
    }]
  }
})
</script>

<template>
  <Head title="Analytics Dashboard" />
  
  <DashboardLayout>
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
          <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Analytics Dashboard
            </h2>
            <p class="mt-1 text-sm text-gray-500">
              Insights and metrics for your accommodation business
            </p>
          </div>
          <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
            <SecondaryButton @click="showFilters = !showFilters" class="inline-flex items-center">
              <Filter class="w-4 h-4 mr-2" />
              Filters
            </SecondaryButton>
            <PrimaryButton @click="fetchAnalyticsData" :disabled="loading" class="inline-flex items-center">
              <TrendingUp class="w-4 h-4 mr-2" />
              {{ loading ? 'Loading...' : 'Refresh' }}
            </PrimaryButton>
          </div>
        </div>

        <!-- Filters Panel -->
        <Card v-if="showFilters" class="mb-6">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Options</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <!-- Period Selection -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Time Period</label>
                <select v-model="selectedPeriod" class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                  <option v-for="option in periodOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </option>
                </select>
              </div>

              <!-- Custom Date Range -->
              <div v-if="isCustomPeriod" class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <div class="flex space-x-2">
                  <input
                    v-model="customStartDate"
                    type="date"
                    class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                  />
                  <input
                    v-model="customEndDate"
                    type="date"
                    class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>
              </div>

              <!-- Accommodation Groups -->
              <div v-if="accommodationGroups.length > 0">
                <label class="block text-sm font-medium text-gray-700 mb-2">Groups</label>
                <select v-model="selectedGroups" multiple class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                  <option v-for="group in accommodationGroups" :key="group.id" :value="group.id">
                    {{ group.name }}
                  </option>
                </select>
              </div>

              <!-- Specific Accommodations -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Accommodations</label>
                <select v-model="selectedAccommodations" multiple class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                  <option v-for="accommodation in filteredAccommodations" :key="accommodation.id" :value="accommodation.id">
                    {{ accommodation.name }}
                  </option>
                </select>
              </div>
            </div>

            <div class="mt-4 flex justify-end space-x-3">
              <SecondaryButton @click="resetFilters">Reset Filters</SecondaryButton>
              <PrimaryButton @click="fetchAnalyticsData">Apply Filters</PrimaryButton>
            </div>
          </div>
        </Card>

        <!-- Error Message -->
        <div v-if="error" class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <AlertCircle class="h-5 w-5 text-red-400" />
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error Loading Analytics</h3>
              <p class="mt-1 text-sm text-red-700">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading && !analyticsData" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <p class="mt-2 text-sm text-gray-500">Loading analytics data...</p>
        </div>

        <!-- Analytics Content -->
        <div v-else-if="analyticsData" class="space-y-8">
          <!-- Insights Panel -->
          <div v-if="insights.length > 0" class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-center mb-4">
              <Lightbulb class="h-6 w-6 text-blue-600 mr-2" />
              <h3 class="text-lg font-semibold text-blue-900">Business Insights</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="insight in insights.slice(0, 6)" :key="insight.metric + insight.title"
                   :class="[
                     'p-4 rounded-lg border-l-4',
                     insight.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                     insight.type === 'success' ? 'bg-green-50 border-green-400' :
                     'bg-blue-50 border-blue-400'
                   ]">
                <h4 :class="[
                  'font-medium text-sm mb-1',
                  insight.type === 'warning' ? 'text-yellow-800' :
                  insight.type === 'success' ? 'text-green-800' :
                  'text-blue-800'
                ]">{{ insight.title }}</h4>
                <p :class="[
                  'text-sm mb-2',
                  insight.type === 'warning' ? 'text-yellow-700' :
                  insight.type === 'success' ? 'text-green-700' :
                  'text-blue-700'
                ]">{{ insight.message }}</p>
                <p :class="[
                  'text-xs font-medium',
                  insight.type === 'warning' ? 'text-yellow-600' :
                  insight.type === 'success' ? 'text-green-600' :
                  'text-blue-600'
                ]">💡 {{ insight.action }}</p>
              </div>
            </div>
          </div>

          <!-- Key Metrics Cards -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Searches"
              :value="conversionMetrics?.total_searches || 0"
              :icon="Users"
              color="blue"
              tooltip="Total number of searches for your properties. Indicates market interest and visibility."
            />

            <MetricCard
              title="Conversion Rate"
              :value="conversionMetrics?.conversion_rate || 0"
              :icon="Target"
              color="green"
              format="percentage"
              tooltip="Percentage of available searches that resulted in confirmed bookings. Higher is better."
            />

            <MetricCard
              title="Availability Rate"
              :value="conversionMetrics?.availability_rate || 0"
              :icon="Calendar"
              color="orange"
              format="percentage"
              tooltip="Percentage of searches where your property was available. Low rates indicate high occupancy or calendar blocks."
            />

            <MetricCard
              title="Confirmed Bookings"
              :value="conversionMetrics?.confirmed_bookings || 0"
              :icon="DollarSign"
              color="purple"
              tooltip="Number of bookings that were confirmed during this period."
            />
          </div>

          <!-- Charts Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Accommodation Popularity -->
            <Card class="p-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Most Popular Accommodations</h3>
                <SecondaryButton @click="exportData('popularity')" class="inline-flex items-center text-sm">
                  <Download class="w-4 h-4 mr-1" />
                  Export
                </SecondaryButton>
              </div>
              <BarChart
                :key="`popularity-${selectedPeriod}-${customStartDate}-${customEndDate}`"
                :labels="popularityChartData.labels"
                :datasets="popularityChartData.datasets"
                height="300px"
                horizontal
              />
            </Card>

            <!-- Unavailability Reasons -->
            <Card class="p-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Unavailability Reasons</h3>
                <SecondaryButton @click="exportData('unavailability')" class="inline-flex items-center text-sm">
                  <Download class="w-4 h-4 mr-1" />
                  Export
                </SecondaryButton>
              </div>
              <PieChart
                :key="`unavailability-${selectedPeriod}-${customStartDate}-${customEndDate}`"
                :labels="unavailabilityReasonsData.labels"
                :data="unavailabilityReasonsData.data"
                height="300px"
                doughnut
              />
            </Card>
          </div>

          <!-- Search Trends -->
          <Card class="p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">Search Volume Trends</h3>
              <SecondaryButton @click="exportData('trends')" class="inline-flex items-center text-sm">
                <Download class="w-4 h-4 mr-1" />
                Export
              </SecondaryButton>
            </div>
            <TimeSeriesChart
              :key="`trends-${selectedPeriod}-${customStartDate}-${customEndDate}`"
              :datasets="searchTrendsData.datasets"
              height="400px"
              time-unit="day"
              :fill="true"
            />
          </Card>
        </div>
      </div>
    </div>
  </DashboardLayout>
</template>
