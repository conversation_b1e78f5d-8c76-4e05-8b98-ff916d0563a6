<template>
	<section id="features" class="py-16 md:py-24 px-4 bg-white">
		<div class="container mx-auto max-w-7xl">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">
					Powerful Features to Grow Your Business
				</h2>
				<p class="text-lg text-gray-700 max-w-3xl mx-auto">
					Booking Bear provides all the tools you need to manage your
					accommodations efficiently. Along with a dead simple way to take bookings on your own website.
				</p>
			</div>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				<div v-for="(feature, index) in features" :key="index"
					class="bg-background p-6 rounded-lg hover:shadow-lg transition-shadow">
					<div class="bg-white inline-block p-3 rounded-lg mb-4">
						<component :is="feature.icon" class="w-8 h-8 text-accent-primary" />
					</div>
					<h3 class="text-xl font-semibold text-primary mb-3">
						{{ feature.title }}
					</h3>
					<p class="text-gray-700">{{ feature.description }}</p>
				</div>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
import {
	Code as CodeIcon,
	Lightbulb as LightbulbIcon,
	Calendar as CalendarIcon,
	BarChart as BarChartIcon,
	BookOpenCheck as BookOpenCheckIcon,
	CreditCard as CreditCardIcon,
	Users as UsersIcon,
	Globe as GlobeIcon
} from 'lucide-vue-next'

const features = [
	{
		icon: LightbulbIcon,
		title: 'Easy Setup on Any Site',
		description: 'Add a simple embed code to your website and instantly let visitors check availability and request bookings—no coding needed.'
	},
	{
		icon: BookOpenCheckIcon,
		title: 'Stay in Control of Your Bookings',
		description: 'Track booking requests, manage availability in real-time, and avoid double bookings with ease.'
	},
	{
		icon: CreditCardIcon,
		title: 'Flexible Pricing Options',
		description: 'Customize pricing based on minimum/maximum occupancy, seasonal rates, and more.'
	},
	{
		icon: CalendarIcon,
		title: 'Availability That Matches Your Schedule',
		description: 'Block out specific dates or set recurring unavailable days—your calendar, your rules.'
	},
	{
		icon: CodeIcon,
		title: 'Developer-Friendly Integrations (Coming Soon)',
		description: 'Use our upcoming REST API or CMS plugins like WordPress to build deeper integrations and custom booking flows.'
	},
	{
		icon: BarChartIcon,
		title: 'Booking Insights & Analytics',
		description: 'Get data on occupancy, booking trends, and availability search patterns to optimize your performance.'
	},
]

</script>
