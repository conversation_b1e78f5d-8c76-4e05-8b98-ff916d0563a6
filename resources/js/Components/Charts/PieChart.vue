<template>
  <BaseChart
    :type="doughnut ? 'doughnut' : 'pie'"
    :data="chartData"
    :options="chartOptions"
    :height="height"
    :width="width"
    @chart-created="$emit('chart-created', $event)"
    @chart-updated="$emit('chart-updated', $event)"
  />
</template>

<script setup>
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'

const props = defineProps({
  labels: {
    type: Array,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  width: {
    type: String,
    default: '100%'
  },
  doughnut: {
    type: Boolean,
    default: false
  },
  showPercentages: {
    type: Boolean,
    default: true
  },
  currency: {
    type: Boolean,
    default: false
  },
  colors: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['chart-created', 'chart-updated'])

// Default colors for pie slices
const defaultColors = [
  '#f97316', // orange-500
  '#06b6d4', // cyan-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#3b82f6', // blue-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#64748b', // slate-500
  '#78716c'  // stone-500
]

const chartData = computed(() => {
  const colors = props.colors.length > 0 ? props.colors : defaultColors
  const backgroundColors = colors.slice(0, props.data.length)
  const borderColors = backgroundColors.map(color => color)

  return {
    labels: props.labels,
    datasets: [{
      data: props.data,
      backgroundColor: backgroundColors,
      borderColor: borderColors,
      borderWidth: 2,
      hoverBorderWidth: 3,
      hoverOffset: 4
    }]
  }
})

const chartOptions = computed(() => {
  const total = props.data.reduce((sum, value) => sum + value, 0)
  
  return {
    plugins: {
      title: {
        display: !!props.title,
        text: props.title,
        font: {
          size: 16,
          weight: 'bold'
        },
        padding: 20
      },
      legend: {
        display: true,
        position: 'right',
        labels: {
          usePointStyle: true,
          padding: 15,
          generateLabels: function(chart) {
            const data = chart.data
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label, i) => {
                const value = data.datasets[0].data[i]
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0
                
                let displayLabel = label
                if (props.showPercentages) {
                  displayLabel += ` (${percentage}%)`
                }
                
                return {
                  text: displayLabel,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor[i],
                  lineWidth: data.datasets[0].borderWidth,
                  pointStyle: 'circle',
                  hidden: false,
                  index: i
                }
              })
            }
            return []
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.parsed
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0
            
            let displayValue = value.toLocaleString()
            if (props.currency) {
              displayValue = 'R' + value.toLocaleString('en-ZA', { minimumFractionDigits: 2 })
            }
            
            return `${label}: ${displayValue} (${percentage}%)`
          }
        }
      }
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1000,
      easing: 'easeInOutQuart'
    },
    cutout: props.doughnut ? '60%' : '0%',
    radius: '90%',
    maintainAspectRatio: false
  }
})
</script>
