<template>
  <BaseChart
    type="bar"
    :data="chartData"
    :options="chartOptions"
    :height="height"
    :width="width"
    @chart-created="$emit('chart-created', $event)"
    @chart-updated="$emit('chart-updated', $event)"
  />
</template>

<script setup>
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'

const props = defineProps({
  labels: {
    type: Array,
    required: true
  },
  datasets: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  width: {
    type: String,
    default: '100%'
  },
  horizontal: {
    type: Boolean,
    default: false
  },
  stacked: {
    type: Boolean,
    default: false
  },
  showValues: {
    type: Boolean,
    default: false
  },
  currency: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['chart-created', 'chart-updated'])

// Default colors for datasets
const defaultColors = [
  '#f97316', // orange-500
  '#06b6d4', // cyan-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#3b82f6', // blue-500
  '#8b5cf6', // violet-500
  '#ec4899'  // pink-500
]

const chartData = computed(() => {
  const processedDatasets = props.datasets.map((dataset, index) => ({
    label: dataset.label || `Dataset ${index + 1}`,
    data: dataset.data || [],
    backgroundColor: dataset.backgroundColor || defaultColors[index % defaultColors.length] + '80', // 50% opacity
    borderColor: dataset.borderColor || defaultColors[index % defaultColors.length],
    borderWidth: dataset.borderWidth || 2,
    borderRadius: dataset.borderRadius || 4,
    borderSkipped: false,
    ...dataset
  }))

  return {
    labels: props.labels,
    datasets: processedDatasets
  }
})

const chartOptions = computed(() => ({
  indexAxis: props.horizontal ? 'y' : 'x',
  plugins: {
    title: {
      display: !!props.title,
      text: props.title,
      font: {
        size: 16,
        weight: 'bold'
      },
      padding: 20
    },
    legend: {
      display: props.datasets.length > 1,
      position: 'top'
    },
    tooltip: {
      callbacks: {
        label: function(context) {
          let label = context.dataset.label || ''
          if (label) {
            label += ': '
          }
          
          const value = context.parsed.y || context.parsed.x
          if (props.currency) {
            label += 'R' + value.toLocaleString('en-ZA', { minimumFractionDigits: 2 })
          } else {
            label += value.toLocaleString()
          }
          
          return label
        }
      }
    }
  },
  scales: {
    x: {
      stacked: props.stacked,
      grid: {
        display: !props.horizontal
      },
      ticks: {
        callback: function(value, index) {
          const label = this.getLabelForValue(value)
          if (typeof label === 'string' && label.length > 15) {
            return label.substring(0, 15) + '...'
          }
          return label
        }
      }
    },
    y: {
      stacked: props.stacked,
      grid: {
        display: props.horizontal
      },
      ticks: {
        callback: function(value) {
          if (props.currency) {
            return 'R' + value.toLocaleString('en-ZA')
          }
          return value.toLocaleString()
        }
      }
    }
  },
  animation: {
    duration: 1000,
    easing: 'easeInOutQuart'
  },
  interaction: {
    intersect: false,
    mode: 'index'
  }
}))
</script>
