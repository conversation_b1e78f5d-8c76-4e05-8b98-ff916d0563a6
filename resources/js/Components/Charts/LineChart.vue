<template>
  <BaseChart
    type="line"
    :data="chartData"
    :options="chartOptions"
    :height="height"
    :width="width"
    @chart-created="$emit('chart-created', $event)"
    @chart-updated="$emit('chart-updated', $event)"
  />
</template>

<script setup>
import { computed } from 'vue'
import BaseChart from './BaseChart.vue'

const props = defineProps({
  labels: {
    type: Array,
    required: true
  },
  datasets: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '400px'
  },
  width: {
    type: String,
    default: '100%'
  },
  smooth: {
    type: Boolean,
    default: true
  },
  fill: {
    type: Boolean,
    default: false
  },
  showPoints: {
    type: Boolean,
    default: true
  },
  currency: {
    type: Boolean,
    default: false
  },
  timeScale: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['chart-created', 'chart-updated'])

// Default colors for datasets
const defaultColors = [
  '#f97316', // orange-500
  '#06b6d4', // cyan-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#3b82f6', // blue-500
  '#8b5cf6', // violet-500
  '#ec4899'  // pink-500
]

const chartData = computed(() => {
  const processedDatasets = props.datasets.map((dataset, index) => ({
    label: dataset.label || `Dataset ${index + 1}`,
    data: dataset.data || [],
    borderColor: dataset.borderColor || defaultColors[index % defaultColors.length],
    backgroundColor: dataset.backgroundColor || (props.fill ? defaultColors[index % defaultColors.length] + '20' : 'transparent'),
    borderWidth: dataset.borderWidth || 3,
    pointBackgroundColor: dataset.pointBackgroundColor || defaultColors[index % defaultColors.length],
    pointBorderColor: dataset.pointBorderColor || '#ffffff',
    pointBorderWidth: dataset.pointBorderWidth || 2,
    pointRadius: props.showPoints ? (dataset.pointRadius || 4) : 0,
    pointHoverRadius: dataset.pointHoverRadius || 6,
    fill: dataset.fill !== undefined ? dataset.fill : props.fill,
    tension: props.smooth ? (dataset.tension || 0.4) : 0,
    ...dataset
  }))

  return {
    labels: props.labels,
    datasets: processedDatasets
  }
})

const chartOptions = computed(() => ({
  plugins: {
    title: {
      display: !!props.title,
      text: props.title,
      font: {
        size: 16,
        weight: 'bold'
      },
      padding: 20
    },
    legend: {
      display: props.datasets.length > 1,
      position: 'top'
    },
    tooltip: {
      callbacks: {
        label: function(context) {
          let label = context.dataset.label || ''
          if (label) {
            label += ': '
          }
          
          const value = context.parsed.y
          if (props.currency) {
            label += 'R' + value.toLocaleString('en-ZA', { minimumFractionDigits: 2 })
          } else {
            label += value.toLocaleString()
          }
          
          return label
        }
      }
    }
  },
  scales: {
    x: props.timeScale ? {
      type: 'time',
      time: {
        displayFormats: {
          day: 'MMM dd',
          week: 'MMM dd',
          month: 'MMM yyyy'
        }
      },
      grid: {
        display: false
      }
    } : {
      grid: {
        display: false
      },
      ticks: {
        callback: function(value, index) {
          const label = this.getLabelForValue(value)
          if (typeof label === 'string' && label.length > 10) {
            return label.substring(0, 10) + '...'
          }
          return label
        }
      }
    },
    y: {
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)'
      },
      ticks: {
        callback: function(value) {
          if (props.currency) {
            return 'R' + value.toLocaleString('en-ZA')
          }
          return value.toLocaleString()
        }
      }
    }
  },
  animation: {
    duration: 1500,
    easing: 'easeInOutQuart'
  },
  interaction: {
    intersect: false,
    mode: 'index'
  },
  elements: {
    line: {
      borderJoinStyle: 'round'
    },
    point: {
      hoverBorderWidth: 3
    }
  }
}))
</script>
