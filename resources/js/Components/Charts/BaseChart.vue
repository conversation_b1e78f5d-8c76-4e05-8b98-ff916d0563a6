<template>
  <div class="chart-container" :style="{ height: height, width: width }">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script setup>
import { ref, shallowRef, onMounted, onUnmounted, watch, nextTick, markRaw } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  TimeScale,
  Filler,
  LineController,
  BarController,
  PieController,
  DoughnutController,
  RadarController,
  PolarAreaController
} from 'chart.js'
import 'chartjs-adapter-date-fns'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  TimeScale,
  Filler,
  LineController,
  BarController,
  PieController,
  DoughnutController,
  RadarController,
  PolarAreaController
)

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => ['line', 'bar', 'pie', 'doughnut', 'radar', 'polarArea'].includes(value)
  },
  data: {
    type: Object,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: String,
    default: '400px'
  },
  width: {
    type: String,
    default: '100%'
  },
  responsive: {
    type: Boolean,
    default: true
  },
  maintainAspectRatio: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['chart-created', 'chart-updated', 'chart-destroyed'])

const chartCanvas = ref(null)
const chartInstance = shallowRef(null)

// Default theme colors
const defaultColors = {
  primary: '#f97316',      // orange-500
  secondary: '#06b6d4',    // cyan-500
  success: '#10b981',      // emerald-500
  warning: '#f59e0b',      // amber-500
  danger: '#ef4444',       // red-500
  info: '#3b82f6',         // blue-500
  light: '#f8fafc',        // slate-50
  dark: '#1e293b'          // slate-800
}

const getDefaultOptions = () => ({
  responsive: props.responsive,
  maintainAspectRatio: props.maintainAspectRatio,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          size: 12,
          family: "'Inter', sans-serif"
        }
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#ffffff',
      bodyColor: '#ffffff',
      borderColor: defaultColors.primary,
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: true,
      titleFont: {
        size: 14,
        weight: 'bold'
      },
      bodyFont: {
        size: 12
      }
    }
  },
  scales: props.type === 'pie' || props.type === 'doughnut' ? {} : {
    x: {
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
        borderColor: 'rgba(0, 0, 0, 0.1)'
      },
      ticks: {
        font: {
          size: 11,
          family: "'Inter', sans-serif"
        }
      }
    },
    y: {
      grid: {
        color: 'rgba(0, 0, 0, 0.1)',
        borderColor: 'rgba(0, 0, 0, 0.1)'
      },
      ticks: {
        font: {
          size: 11,
          family: "'Inter', sans-serif"
        }
      }
    }
  }
})

const mergeOptions = (defaultOpts, userOpts) => {
  return {
    ...defaultOpts,
    ...userOpts,
    plugins: {
      ...defaultOpts.plugins,
      ...userOpts.plugins
    },
    scales: {
      ...defaultOpts.scales,
      ...userOpts.scales
    }
  }
}

const createChart = async () => {
  if (!chartCanvas.value) return

  await nextTick()

  const ctx = chartCanvas.value.getContext('2d')
  const defaultOptions = getDefaultOptions()
  const mergedOptions = mergeOptions(defaultOptions, props.options)

  chartInstance.value = markRaw(new ChartJS(ctx, {
    type: props.type,
    data: props.data,
    options: mergedOptions
  }))

  emit('chart-created', chartInstance.value)
}

let updateTimeout = null

const updateChart = () => {
  if (!chartInstance.value) return

  // Clear any pending updates
  if (updateTimeout) {
    clearTimeout(updateTimeout)
  }

  // Debounce updates to prevent rapid re-renders
  updateTimeout = setTimeout(() => {
    if (chartInstance.value) {
      chartInstance.value.data = props.data
      chartInstance.value.options = mergeOptions(getDefaultOptions(), props.options)
      chartInstance.value.update('none') // Use 'none' instead of 'active' for better performance

      emit('chart-updated', chartInstance.value)
    }
  }, 50) // 50ms debounce
}

const destroyChart = () => {
  // Clear any pending updates
  if (updateTimeout) {
    clearTimeout(updateTimeout)
    updateTimeout = null
  }

  if (chartInstance.value) {
    chartInstance.value.destroy()
    chartInstance.value = null
    emit('chart-destroyed')
  }
}

// Watch for data changes with less aggressive deep watching
watch(() => props.data, (newData, oldData) => {
  // Only update if data actually changed
  if (JSON.stringify(newData) !== JSON.stringify(oldData)) {
    updateChart()
  }
}, { deep: true, flush: 'post' })

// Watch for options changes
watch(() => props.options, (newOptions, oldOptions) => {
  // Only update if options actually changed
  if (JSON.stringify(newOptions) !== JSON.stringify(oldOptions)) {
    updateChart()
  }
}, { deep: true, flush: 'post' })

onMounted(() => {
  createChart()
})

onUnmounted(() => {
  destroyChart()
})

// Expose chart instance for parent components
defineExpose({
  chartInstance,
  updateChart,
  destroyChart
})
</script>

<style scoped>
.chart-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}
</style>
