<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <!-- Icon -->
        <div :class="[
          'flex items-center justify-center w-12 h-12 rounded-lg',
          iconBackgroundClass
        ]">
          <component :is="icon" :class="['w-6 h-6', iconColorClass]" />
        </div>
        
        <!-- Content -->
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-600 mb-1">{{ title }}</p>
          <p class="text-2xl font-bold text-gray-900">{{ formattedValue }}</p>
          
          <!-- Change indicator -->
          <div v-if="change !== null && change !== undefined" class="flex items-center mt-2">
            <component 
              :is="changeIcon" 
              :class="['w-4 h-4 mr-1', changeColorClass]" 
            />
            <span :class="['text-sm font-medium', changeColorClass]">
              {{ Math.abs(change) }}{{ changeType === 'percentage' ? '%' : '' }}
            </span>
            <span class="text-sm text-gray-500 ml-1">{{ changePeriod }}</span>
          </div>
        </div>
      </div>
      
      <!-- Tooltip/Info -->
      <div v-if="tooltip" class="relative group">
        <button class="text-gray-400 hover:text-gray-600 transition-colors">
          <HelpCircle class="w-5 h-5" />
        </button>
        <div class="absolute right-0 top-8 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
          {{ tooltip }}
          <div class="absolute -top-1 right-4 w-2 h-2 bg-gray-900 transform rotate-45"></div>
        </div>
      </div>
    </div>
    
    <!-- Insight/Recommendation -->
    <div v-if="insight" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start space-x-2">
        <Lightbulb class="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
        <p class="text-sm text-blue-800">{{ insight }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  HelpCircle, 
  Lightbulb 
} from 'lucide-vue-next'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  icon: {
    type: Object,
    required: true
  },
  change: {
    type: Number,
    default: null
  },
  changeType: {
    type: String,
    default: 'percentage',
    validator: (value) => ['percentage', 'absolute'].includes(value)
  },
  changePeriod: {
    type: String,
    default: 'vs last period'
  },
  format: {
    type: String,
    default: 'number',
    validator: (value) => ['number', 'currency', 'percentage'].includes(value)
  },
  color: {
    type: String,
    default: 'blue',
    validator: (value) => ['blue', 'green', 'orange', 'purple', 'red', 'gray'].includes(value)
  },
  tooltip: {
    type: String,
    default: null
  },
  insight: {
    type: String,
    default: null
  }
})

// Color mappings
const colorClasses = {
  blue: {
    icon: 'text-blue-600',
    background: 'bg-blue-100'
  },
  green: {
    icon: 'text-green-600',
    background: 'bg-green-100'
  },
  orange: {
    icon: 'text-orange-600',
    background: 'bg-orange-100'
  },
  purple: {
    icon: 'text-purple-600',
    background: 'bg-purple-100'
  },
  red: {
    icon: 'text-red-600',
    background: 'bg-red-100'
  },
  gray: {
    icon: 'text-gray-600',
    background: 'bg-gray-100'
  }
}

// Computed properties
const iconColorClass = computed(() => colorClasses[props.color].icon)
const iconBackgroundClass = computed(() => colorClasses[props.color].background)

const formattedValue = computed(() => {
  const value = props.value
  
  if (typeof value === 'string') return value
  
  switch (props.format) {
    case 'currency':
      return 'R' + value.toLocaleString('en-ZA', { 
        minimumFractionDigits: 2,
        maximumFractionDigits: 2 
      })
    case 'percentage':
      return value.toFixed(1) + '%'
    case 'number':
    default:
      return value.toLocaleString()
  }
})

const changeIcon = computed(() => {
  if (props.change === null || props.change === undefined) return null
  if (props.change > 0) return TrendingUp
  if (props.change < 0) return TrendingDown
  return Minus
})

const changeColorClass = computed(() => {
  if (props.change === null || props.change === undefined) return 'text-gray-500'
  if (props.change > 0) return 'text-green-600'
  if (props.change < 0) return 'text-red-600'
  return 'text-gray-500'
})
</script>
