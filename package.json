{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.14", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^5.0.0", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.32", "tailwindcss": "^4.0.14", "vite": "^6.0.11", "vue": "^3.3.13"}, "dependencies": {"@agentdeskai/browser-tools-mcp": "^1.2.0", "@agentdeskai/browser-tools-server": "^1.2.0", "@feedback-fish/vue": "^1.0.1", "@preline/collapse": "^3.0.0", "@schedule-x/calendar": "^2.28.0", "@schedule-x/event-recurrence": "^2.28.0", "@schedule-x/events-service": "^2.28.0", "@schedule-x/theme-default": "^2.28.0", "@schedule-x/vue": "^2.26.0", "@vuepic/vue-datepicker": "^11.0.2", "@vueup/vue-quill": "^1.2.0", "@vueuse/motion": "^3.0.3", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-vue-next": "^0.507.0", "preline": "^3.0.1", "vue-chartjs": "^5.3.2", "vue-gtag": "^3.5.0", "vuedraggable": "^4.1.0"}}