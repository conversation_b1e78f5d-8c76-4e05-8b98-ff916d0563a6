<?php

namespace Database\Seeders;

use App\Models\Accommodation;
use App\Models\AccommodationSearch;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class AccommodationSearchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the admin user
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $this->command->warn('Admin user not found. Please run the main DatabaseSeeder first.');
            return;
        }

        // Get user's accommodations
        $accommodations = $user->accommodations;
        
        if ($accommodations->isEmpty()) {
            $this->command->warn('No accommodations found for user. Please create accommodations first.');
            return;
        }

        $this->command->info('Creating accommodation search data...');

        // Define unavailability reasons
        $unavailabilityReasons = [
            'Already booked',
            'Minimum stay not met',
            'Blocked by owner',
            'Maintenance period',
            'Minimum booking notice not met',
            'Property not available'
        ];

        // Create searches for the last 90 days
        $startDate = Carbon::now()->subDays(90);
        $endDate = Carbon::now();

        $searchCount = 0;

        // Generate searches for each day
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            // Vary search volume by day of week (more on weekends)
            $baseSearches = $date->isWeekend() ? rand(8, 20) : rand(3, 12);
            
            // Add seasonal variation (more in summer months)
            $month = $date->month;
            if (in_array($month, [12, 1, 2])) { // Summer in South Africa
                $baseSearches = (int) ($baseSearches * 1.5);
            }

            foreach ($accommodations as $accommodation) {
                // Each accommodation gets some searches
                $dailySearches = rand(0, $baseSearches);
                
                for ($i = 0; $i < $dailySearches; $i++) {
                    // Random search time during the day
                    $searchTime = $date->copy()->addHours(rand(6, 22))->addMinutes(rand(0, 59));
                    
                    // Random stay duration (1-14 days)
                    $stayDuration = rand(1, 14);
                    
                    // Random start date (within next 6 months)
                    $stayStart = $searchTime->copy()->addDays(rand(1, 180));
                    $stayEnd = $stayStart->copy()->addDays($stayDuration);
                    
                    // Random occupancy
                    $occupancy = rand($accommodation->min_occupancy, $accommodation->max_occupancy);
                    
                    // Determine availability (70% available, 30% unavailable)
                    $wasAvailable = rand(1, 100) <= 70;
                    
                    $unavailabilityReason = null;
                    $quotedPrice = null;
                    
                    if ($wasAvailable) {
                        // Calculate a quoted price (simplified)
                        $basePrice = rand(800, 3000); // R800 - R3000 per night
                        $quotedPrice = $basePrice * $stayDuration;
                        
                        // Add weekend premium
                        if ($stayStart->isWeekend() || $stayEnd->isWeekend()) {
                            $quotedPrice *= 1.2;
                        }
                    } else {
                        // Pick a random unavailability reason
                        $unavailabilityReason = $unavailabilityReasons[array_rand($unavailabilityReasons)];
                    }

                    AccommodationSearch::create([
                        'accommodation_id' => $accommodation->id,
                        'user_id' => $user->id,
                        'start_date' => $stayStart,
                        'end_date' => $stayEnd,
                        'requested_occupancy' => $occupancy,
                        'was_available' => $wasAvailable,
                        'unavailability_reason' => $unavailabilityReason,
                        'quoted_price' => $quotedPrice,
                        'searched_at' => $searchTime
                    ]);

                    $searchCount++;
                }
            }
        }

        $this->command->info("Created {$searchCount} accommodation searches for analytics testing.");
        
        // Create some additional high-volume searches for popular properties
        if ($accommodations->count() > 1) {
            $popularProperty = $accommodations->first();
            
            $this->command->info("Adding extra searches for popular property: {$popularProperty->name}");
            
            for ($i = 0; $i < 200; $i++) {
                $searchTime = Carbon::now()->subDays(rand(1, 30))->addHours(rand(6, 22));
                $stayStart = $searchTime->copy()->addDays(rand(1, 60));
                $stayEnd = $stayStart->copy()->addDays(rand(2, 7));
                
                $wasAvailable = rand(1, 100) <= 80; // Higher availability for popular property
                
                AccommodationSearch::create([
                    'accommodation_id' => $popularProperty->id,
                    'user_id' => $user->id,
                    'start_date' => $stayStart,
                    'end_date' => $stayEnd,
                    'requested_occupancy' => rand($popularProperty->min_occupancy, $popularProperty->max_occupancy),
                    'was_available' => $wasAvailable,
                    'unavailability_reason' => $wasAvailable ? null : $unavailabilityReasons[array_rand($unavailabilityReasons)],
                    'quoted_price' => $wasAvailable ? rand(1500, 4000) * rand(2, 7) : null,
                    'searched_at' => $searchTime
                ]);
            }
            
            $this->command->info("Added 200 additional searches for popular property.");
        }
    }
}
