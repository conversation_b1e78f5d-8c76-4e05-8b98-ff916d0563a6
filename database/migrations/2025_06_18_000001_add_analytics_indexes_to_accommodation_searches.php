<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accommodation_searches', function (Blueprint $table) {
            // Composite indexes for analytics queries
            $table->index(['user_id', 'searched_at', 'was_available'], 'idx_user_searched_available');
            $table->index(['accommodation_id', 'searched_at', 'was_available'], 'idx_accommodation_searched_available');
            $table->index(['searched_at', 'was_available'], 'idx_searched_available');
            $table->index(['start_date', 'end_date', 'searched_at'], 'idx_dates_searched');
            $table->index(['unavailability_reason', 'was_available'], 'idx_unavailability_reason');
            $table->index(['quoted_price', 'searched_at'], 'idx_price_searched');
            
            // Index for date range analysis
            $table->index(['start_date', 'accommodation_id'], 'idx_start_date_accommodation');
            $table->index(['end_date', 'accommodation_id'], 'idx_end_date_accommodation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accommodation_searches', function (Blueprint $table) {
            $table->dropIndex('idx_user_searched_available');
            $table->dropIndex('idx_accommodation_searched_available');
            $table->dropIndex('idx_searched_available');
            $table->dropIndex('idx_dates_searched');
            $table->dropIndex('idx_unavailability_reason');
            $table->dropIndex('idx_price_searched');
            $table->dropIndex('idx_start_date_accommodation');
            $table->dropIndex('idx_end_date_accommodation');
        });
    }
};
